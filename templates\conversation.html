{% extends "base.html" %}

{% block title %}Conversation with {{ phone_number }} - SMS Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-chat-dots"></i> Conversation with {{ phone_number }}
                {% if messages %}
                    <small class="text-muted">({{ messages|length }} messages)</small>
                {% endif %}
            </h1>
            <div>
                <a href="{{ url_for('inbox') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Inbox
                </a>
                <a href="{{ url_for('compose', to=phone_number) }}" class="btn btn-primary">
                    <i class="bi bi-reply"></i> Send Message
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Conversation Messages -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-chat-text"></i> Message History
                </h5>
                <button onclick="location.reload()" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-arrow-clockwise"></i> Refresh
                </button>
            </div>
            <div class="card-body p-0" style="max-height: 600px; overflow-y: auto;" id="messageContainer">
                {% if messages %}
                    <div class="p-3">
                        {% for message in messages %}
                        <div class="message-bubble mb-3 {% if message.direction == 'outbound' %}outbound{% else %}inbound{% endif %}">
                            <div class="d-flex {% if message.direction == 'outbound' %}justify-content-end{% else %}justify-content-start{% endif %}">
                                <div class="message-content {% if message.direction == 'outbound' %}bg-primary text-white{% else %}bg-light{% endif %} rounded p-3" style="max-width: 70%;">
                                    <div class="message-text">
                                        {{ message.content }}
                                    </div>
                                    <div class="message-meta mt-2 pt-2 border-top {% if message.direction == 'outbound' %}border-light{% else %}border-secondary{% endif %} opacity-75">
                                        <small class="d-flex justify-content-between align-items-center">
                                            <span>
                                                {% if message.direction == 'outbound' %}
                                                    <i class="bi bi-send"></i> Sent
                                                {% else %}
                                                    <i class="bi bi-inbox"></i> Received
                                                {% endif %}
                                            </span>
                                            <span>{{ message.created_at.strftime('%m/%d %H:%M') }}</span>
                                        </small>
                                        {% if message.status %}
                                            <small class="d-block mt-1">
                                                Status:
                                                {% if message.status == 'sent' %}
                                                    <span class="badge bg-success">Sent</span>
                                                {% elif message.status == 'delivered' %}
                                                    <span class="badge bg-success">Delivered</span>
                                                {% elif message.status == 'failed' %}
                                                    <span class="badge bg-danger">Failed</span>
                                                {% elif message.status == 'pending' %}
                                                    <span class="badge bg-warning">Pending</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">{{ message.status|title }}</span>
                                                {% endif %}
                                            </small>
                                        {% endif %}
                                        {% if message.gsm_port %}
                                            <small class="d-block mt-1">
                                                <i class="bi bi-router"></i> Port {{ message.gsm_port }}
                                            </small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-chat-dots text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">No conversation history</h4>
                        <p class="text-muted">Start a conversation by sending a message to {{ phone_number }}</p>
                        <a href="{{ url_for('compose', to=phone_number) }}" class="btn btn-primary">
                            <i class="bi bi-pencil-square"></i> Send First Message
                        </a>
                    </div>
                {% endif %}
            </div>

            {% if messages and messages.items %}
            <!-- Quick Reply Form -->
            <div class="card-footer">
                <form action="{{ url_for('send_sms') }}" method="POST" class="d-flex gap-2" id="quickReplyForm">
                    <input type="hidden" name="phone_number" value="{{ phone_number }}">
                    <div class="flex-grow-1">
                        <textarea class="form-control"
                                  name="message"
                                  rows="2"
                                  placeholder="Type your reply here..."
                                  required
                                  maxlength="1024"
                                  id="quickMessage"></textarea>
                    </div>
                    <div class="d-flex flex-column gap-1">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-send"></i>
                        </button>
                        <small class="text-muted text-center">
                            <span id="quickCharCount">0</span>/1024
                        </small>
                    </div>
                </form>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Conversation Statistics -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-primary">{{ stats.received_count }}</h4>
                <small class="text-muted">Received</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-success">{{ stats.sent_count }}</h4>
                <small class="text-muted">Sent</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-info">{{ stats.total_messages }}</h4>
                <small class="text-muted">Total</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                {% if messages and messages.items %}
                    <h4 class="text-warning">{{ messages.items[0].created_at.strftime('%m/%d') }}</h4>
                    <small class="text-muted">Latest Message</small>
                {% else %}
                    <h4 class="text-muted">-</h4>
                    <small class="text-muted">No Messages</small>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.message-bubble.inbound {
    animation: slideInLeft 0.3s ease-out;
}

.message-bubble.outbound {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.message-content {
    word-wrap: break-word;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

#messageContainer {
    scroll-behavior: smooth;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Scroll to bottom of conversation
    const messageContainer = document.getElementById('messageContainer');
    if (messageContainer) {
        messageContainer.scrollTop = messageContainer.scrollHeight;
    }

    // Character counter for quick reply
    const quickMessage = document.getElementById('quickMessage');
    const quickCharCount = document.getElementById('quickCharCount');

    if (quickMessage && quickCharCount) {
        function updateQuickCharCount() {
            const length = quickMessage.value.length;
            quickCharCount.textContent = length;

            if (length > 1024) {
                quickCharCount.className = 'text-danger text-center';
            } else if (length > 160) {
                quickCharCount.className = 'text-warning text-center';
            } else {
                quickCharCount.className = 'text-muted text-center';
            }
        }

        quickMessage.addEventListener('input', updateQuickCharCount);
        updateQuickCharCount();
    }

    // Auto-refresh every 30 seconds
    setInterval(function() {
        if (document.visibilityState === 'visible') {
            location.reload();
        }
    }, 30000);

    // Handle quick reply form
    const quickReplyForm = document.getElementById('quickReplyForm');
    if (quickReplyForm) {
        quickReplyForm.addEventListener('submit', function(e) {
            const message = quickMessage.value.trim();
            if (!message) {
                e.preventDefault();
                alert('Please enter a message.');
                return;
            }

            if (message.length > 1024) {
                e.preventDefault();
                alert('Message is too long. Maximum 1024 characters allowed.');
                return;
            }
        });
    }
});
</script>
{% endblock %}
