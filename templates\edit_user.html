{% extends "base.html" %}

{% block title %}Edit User - SMS Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-person-gear"></i> Edit User: {{ user.username }}
            </h1>
            <a href="{{ url_for('users') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Users
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-person-gear"></i> User Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('edit_user', user_id=user.id) }}">
                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="username" 
                                   value="{{ user.username }}" 
                                   readonly
                                   disabled>
                            <div class="form-text">Username cannot be changed</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email *</label>
                            <input type="email" 
                                   class="form-control" 
                                   id="email" 
                                   name="email" 
                                   value="{{ user.email }}"
                                   required
                                   placeholder="Enter email address">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">Role *</label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="user" {{ 'selected' if user.role == 'user' else '' }}>User</option>
                                <option value="manager" {{ 'selected' if user.role == 'manager' else '' }}>Manager</option>
                                <option value="admin" {{ 'selected' if user.role == 'admin' else '' }}>Administrator</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="is_active" class="form-label">Status *</label>
                            <select class="form-select" id="is_active" name="is_active" required>
                                <option value="true" {{ 'selected' if user.is_active else '' }}>Active</option>
                                <option value="false" {{ 'selected' if not user.is_active else '' }}>Inactive</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Permissions -->
                    <div class="mb-4">
                        <h6 class="mb-3">
                            <i class="bi bi-shield-check"></i> Permissions
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="can_send_sms" 
                                           name="can_send_sms" 
                                           {{ 'checked' if user.can_send_sms else '' }}>
                                    <label class="form-check-label" for="can_send_sms">
                                        <i class="bi bi-send me-1"></i>Send SMS Messages
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="can_view_inbox" 
                                           name="can_view_inbox" 
                                           {{ 'checked' if user.can_view_inbox else '' }}>
                                    <label class="form-check-label" for="can_view_inbox">
                                        <i class="bi bi-inbox me-1"></i>View Inbox
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="can_view_outbox" 
                                           name="can_view_outbox" 
                                           {{ 'checked' if user.can_view_outbox else '' }}>
                                    <label class="form-check-label" for="can_view_outbox">
                                        <i class="bi bi-send me-1"></i>View Outbox
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="can_export" 
                                           name="can_export" 
                                           {{ 'checked' if user.can_export else '' }}>
                                    <label class="form-check-label" for="can_export">
                                        <i class="bi bi-download me-1"></i>Export Data
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="can_view_reports" 
                                           name="can_view_reports" 
                                           {{ 'checked' if user.can_view_reports else '' }}>
                                    <label class="form-check-label" for="can_view_reports">
                                        <i class="bi bi-graph-up me-1"></i>View Reports
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="can_manage_users" 
                                           name="can_manage_users" 
                                           {{ 'checked' if user.can_manage_users else '' }}>
                                    <label class="form-check-label" for="can_manage_users">
                                        <i class="bi bi-people me-1"></i>Manage Users
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="can_manage_settings" 
                                           name="can_manage_settings" 
                                           {{ 'checked' if user.can_manage_settings else '' }}>
                                    <label class="form-check-label" for="can_manage_settings">
                                        <i class="bi bi-gear me-1"></i>Manage Settings
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="can_manage_ports" 
                                           name="can_manage_ports" 
                                           {{ 'checked' if user.can_manage_ports else '' }}>
                                    <label class="form-check-label" for="can_manage_ports">
                                        <i class="bi bi-hdd-network me-1"></i>Manage Ports
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Port Assignment -->
                    <div class="mb-4">
                        <h6 class="mb-3">
                            <i class="bi bi-hdd-network"></i> Port Assignment
                        </h6>
                        <p class="text-muted small mb-3">
                            Select which SMS ports this user can access. Leave empty to allow access to all ports.
                        </p>
                        {% if ports %}
                            {% set assigned_ports = user.get_assigned_ports() %}
                            <div class="row">
                                {% for port in ports %}
                                <div class="col-md-3 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="port_{{ port.port_number }}" 
                                               name="assigned_ports" 
                                               value="{{ port.port_number }}"
                                               {{ 'checked' if port.port_number in assigned_ports else '' }}>
                                        <label class="form-check-label" for="port_{{ port.port_number }}">
                                            <i class="bi bi-router me-1"></i>Port {{ port.port_number }}
                                            {% if port.status %}
                                                <small class="text-muted">({{ port.status }})</small>
                                            {% endif %}
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                No SMS ports configured yet. User will have access to all ports by default.
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Submit Buttons -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>
                            Update User
                        </button>
                        <a href="{{ url_for('users') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-2"></i>
                            Cancel
                        </a>
                        {% if user.username != 'admin' and user.id != current_user.id %}
                            <button type="button" class="btn btn-outline-danger ms-auto" onclick="deleteUser()">
                                <i class="bi bi-trash me-2"></i>
                                Delete User
                            </button>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- User Info Panel -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle"></i> User Information
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-circle mx-auto mb-2" style="width: 60px; height: 60px; font-size: 1.5rem;">
                        {{ user.username[0].upper() }}
                    </div>
                    <h5>{{ user.username }}</h5>
                    <span class="badge bg-{{ 'danger' if user.role == 'admin' else 'primary' if user.role == 'manager' else 'secondary' }}">
                        {{ user.role.title() }}
                    </span>
                </div>
                
                <ul class="list-unstyled">
                    <li><strong>Email:</strong> {{ user.email }}</li>
                    <li><strong>Status:</strong> 
                        <span class="badge bg-{{ 'success' if user.is_active else 'danger' }}">
                            {{ 'Active' if user.is_active else 'Inactive' }}
                        </span>
                    </li>
                    <li><strong>Created:</strong> {{ user.created_at.strftime('%Y-%m-%d') if user.created_at else 'Unknown' }}</li>
                    <li><strong>Last Login:</strong> {{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'Never' }}</li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-shield-exclamation"></i> Security Notes
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small text-muted mb-0">
                    <li><i class="bi bi-check-circle text-success me-1"></i> Username cannot be changed</li>
                    <li><i class="bi bi-check-circle text-success me-1"></i> Assign only necessary permissions</li>
                    <li><i class="bi bi-check-circle text-success me-1"></i> Limit port access when needed</li>
                    <li><i class="bi bi-check-circle text-success me-1"></i> Review user access regularly</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Auto-update permissions based on role
document.getElementById('role').addEventListener('change', function() {
    const role = this.value;
    const permissions = {
        'admin': ['can_send_sms', 'can_view_inbox', 'can_view_outbox', 'can_export', 'can_view_reports', 'can_manage_users', 'can_manage_settings', 'can_manage_ports'],
        'manager': ['can_send_sms', 'can_view_inbox', 'can_view_outbox', 'can_export', 'can_view_reports', 'can_manage_ports'],
        'user': ['can_send_sms', 'can_view_inbox', 'can_view_outbox']
    };
    
    // Set permissions for selected role
    if (permissions[role]) {
        permissions[role].forEach(permission => {
            const checkbox = document.getElementById(permission);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
        
        // Uncheck permissions not in the role
        document.querySelectorAll('input[type="checkbox"]').forEach(cb => {
            if (cb.name.startsWith('can_') && !permissions[role].includes(cb.id)) {
                cb.checked = false;
            }
        });
    }
});

// Delete user function
function deleteUser() {
    if (confirm(`Are you sure you want to delete user "{{ user.username }}"? This action cannot be undone.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/users/{{ user.id }}/delete`;
        
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
