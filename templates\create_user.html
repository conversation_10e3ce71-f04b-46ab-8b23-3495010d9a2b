{% extends "base.html" %}

{% block title %}Create User - SMS Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-person-plus"></i> Create New User
            </h1>
            <a href="{{ url_for('users') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Users
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-person-gear"></i> User Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('create_user') }}">
                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">Username *</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="username" 
                                   name="username" 
                                   required
                                   placeholder="Enter username">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email *</label>
                            <input type="email" 
                                   class="form-control" 
                                   id="email" 
                                   name="email" 
                                   required
                                   placeholder="Enter email address">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">Password *</label>
                            <input type="password" 
                                   class="form-control" 
                                   id="password" 
                                   name="password" 
                                   required
                                   minlength="6"
                                   placeholder="Enter password">
                            <div class="form-text">Minimum 6 characters</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">Role *</label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="user">User</option>
                                <option value="manager">Manager</option>
                                <option value="admin">Administrator</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Permissions -->
                    <div class="mb-4">
                        <h6 class="mb-3">
                            <i class="bi bi-shield-check"></i> Permissions
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="can_send_sms" 
                                           name="can_send_sms" 
                                           checked>
                                    <label class="form-check-label" for="can_send_sms">
                                        <i class="bi bi-send me-1"></i>Send SMS Messages
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="can_view_inbox" 
                                           name="can_view_inbox" 
                                           checked>
                                    <label class="form-check-label" for="can_view_inbox">
                                        <i class="bi bi-inbox me-1"></i>View Inbox
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="can_view_outbox" 
                                           name="can_view_outbox" 
                                           checked>
                                    <label class="form-check-label" for="can_view_outbox">
                                        <i class="bi bi-send me-1"></i>View Outbox
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="can_export" 
                                           name="can_export">
                                    <label class="form-check-label" for="can_export">
                                        <i class="bi bi-download me-1"></i>Export Data
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="can_view_reports" 
                                           name="can_view_reports">
                                    <label class="form-check-label" for="can_view_reports">
                                        <i class="bi bi-graph-up me-1"></i>View Reports
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="can_manage_users" 
                                           name="can_manage_users">
                                    <label class="form-check-label" for="can_manage_users">
                                        <i class="bi bi-people me-1"></i>Manage Users
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="can_manage_settings" 
                                           name="can_manage_settings">
                                    <label class="form-check-label" for="can_manage_settings">
                                        <i class="bi bi-gear me-1"></i>Manage Settings
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="can_manage_ports" 
                                           name="can_manage_ports">
                                    <label class="form-check-label" for="can_manage_ports">
                                        <i class="bi bi-hdd-network me-1"></i>Manage Ports
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Port Assignment -->
                    <div class="mb-4">
                        <h6 class="mb-3">
                            <i class="bi bi-hdd-network"></i> Port Assignment
                        </h6>
                        <p class="text-muted small mb-3">
                            Select which SMS ports this user can access. Leave empty to allow access to all ports.
                        </p>
                        {% if ports %}
                            <div class="row">
                                {% for port in ports %}
                                <div class="col-md-3 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="port_{{ port.port_number }}" 
                                               name="assigned_ports" 
                                               value="{{ port.port_number }}">
                                        <label class="form-check-label" for="port_{{ port.port_number }}">
                                            <i class="bi bi-router me-1"></i>Port {{ port.port_number }}
                                            {% if port.status %}
                                                <small class="text-muted">({{ port.status }})</small>
                                            {% endif %}
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                No SMS ports configured yet. User will have access to all ports by default.
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Submit Buttons -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-person-plus me-2"></i>
                            Create User
                        </button>
                        <a href="{{ url_for('users') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-2"></i>
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Help Panel -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-question-circle"></i> User Roles & Permissions
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-primary">Administrator</h6>
                    <small class="text-muted">
                        Full system access including user management, settings, and all SMS functions.
                    </small>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-success">Manager</h6>
                    <small class="text-muted">
                        Can manage SMS operations, view reports, and export data. Cannot manage users or system settings.
                    </small>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-info">User</h6>
                    <small class="text-muted">
                        Basic SMS sending and receiving capabilities. Limited access based on assigned permissions.
                    </small>
                </div>
                
                <hr>
                
                <h6 class="mb-2">Permission Guide:</h6>
                <ul class="list-unstyled small text-muted">
                    <li><i class="bi bi-send me-1"></i> <strong>Send SMS:</strong> Compose and send messages</li>
                    <li><i class="bi bi-inbox me-1"></i> <strong>View Inbox:</strong> Access received messages</li>
                    <li><i class="bi bi-download me-1"></i> <strong>Export:</strong> Download message data</li>
                    <li><i class="bi bi-graph-up me-1"></i> <strong>Reports:</strong> View analytics and statistics</li>
                    <li><i class="bi bi-people me-1"></i> <strong>Manage Users:</strong> Create/edit user accounts</li>
                    <li><i class="bi bi-hdd-network me-1"></i> <strong>Manage Ports:</strong> Configure SMS ports</li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-shield-exclamation"></i> Security Notes
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small text-muted mb-0">
                    <li><i class="bi bi-check-circle text-success me-1"></i> Use strong passwords (min 6 characters)</li>
                    <li><i class="bi bi-check-circle text-success me-1"></i> Assign only necessary permissions</li>
                    <li><i class="bi bi-check-circle text-success me-1"></i> Limit port access when needed</li>
                    <li><i class="bi bi-check-circle text-success me-1"></i> Review user access regularly</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-update permissions based on role
document.getElementById('role').addEventListener('change', function() {
    const role = this.value;
    const permissions = {
        'admin': ['can_send_sms', 'can_view_inbox', 'can_view_outbox', 'can_export', 'can_view_reports', 'can_manage_users', 'can_manage_settings', 'can_manage_ports'],
        'manager': ['can_send_sms', 'can_view_inbox', 'can_view_outbox', 'can_export', 'can_view_reports', 'can_manage_ports'],
        'user': ['can_send_sms', 'can_view_inbox', 'can_view_outbox']
    };
    
    // Reset all checkboxes
    document.querySelectorAll('input[type="checkbox"]').forEach(cb => {
        if (cb.name.startsWith('can_')) {
            cb.checked = false;
        }
    });
    
    // Set permissions for selected role
    if (permissions[role]) {
        permissions[role].forEach(permission => {
            const checkbox = document.getElementById(permission);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    }
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const username = document.getElementById('username').value;
    const email = document.getElementById('email').value;
    
    if (password.length < 6) {
        e.preventDefault();
        alert('Password must be at least 6 characters long.');
        return;
    }
    
    if (username.length < 3) {
        e.preventDefault();
        alert('Username must be at least 3 characters long.');
        return;
    }
    
    if (!email.includes('@')) {
        e.preventDefault();
        alert('Please enter a valid email address.');
        return;
    }
});
</script>
{% endblock %}
