{% extends "base.html" %}

{% block title %}Settings - SMS Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-gear"></i> System Settings
            </h1>
            <div class="btn-group">
                <button onclick="location.reload()" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-clockwise"></i> Refresh
                </button>
                <form action="{{ url_for('test_connection') }}" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-outline-success">
                        <i class="bi bi-wifi"></i> Test Connection
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- System Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-activity"></i> System Status
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card bg-{{ 'success' if settings.receiver_status else 'danger' }} text-white">
                            <div class="card-body text-center">
                                <i class="bi bi-{{ 'wifi' if settings.receiver_status else 'wifi-off' }} fs-1"></i>
                                <h6 class="mt-2">SMS Receiver</h6>
                                <small>{{ 'Connected' if settings.receiver_status else 'Disconnected' }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="bi bi-people fs-1"></i>
                                <h6 class="mt-2">Total Users</h6>
                                <small>{{ settings.total_users or 0 }} users</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="bi bi-chat-dots fs-1"></i>
                                <h6 class="mt-2">Total Messages</h6>
                                <small>{{ settings.total_messages or 0 }} messages</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body text-center">
                                <i class="bi bi-hdd-network fs-1"></i>
                                <h6 class="mt-2">SMS Ports</h6>
                                <small>{{ settings.total_ports or 0 }} ports</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-6 mb-4">
        <!-- SMS API Settings -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-cloud"></i> SMS API Configuration
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('update_settings') }}" method="POST" id="smsApiForm">
                    <div class="mb-3">
                        <label for="sms_api_ip" class="form-label">API Server IP Address</label>
                        <input type="text" class="form-control" id="sms_api_ip" name="sms_api_ip"
                               value="{{ settings.sms_api_ip or '*************' }}"
                               placeholder="*************" readonly>
                        <div class="form-text">IP address of your SMS gateway server</div>
                    </div>

                    <div class="mb-3">
                        <label for="sms_api_account" class="form-label">API Account</label>
                        <input type="text" class="form-control" id="sms_api_account" name="sms_api_account"
                               value="{{ settings.sms_api_account or 'apiuser' }}"
                               placeholder="apiuser" readonly>
                    </div>

                    <div class="mb-3">
                        <label for="sms_api_password" class="form-label">API Password</label>
                        <input type="password" class="form-control" id="sms_api_password" name="sms_api_password"
                               value="••••••••" placeholder="••••••••" readonly>
                        <div class="form-text">Password is hidden for security</div>
                    </div>

                    <div class="mb-3">
                        <label for="sms_api_port" class="form-label">Default SMS Port</label>
                        <input type="text" class="form-control" id="sms_api_port" name="sms_api_port"
                               value="{{ settings.sms_api_port or '1' }}"
                               placeholder="1" readonly>
                    </div>

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Note:</strong> SMS API settings are configured via environment variables.
                        Contact your system administrator to modify these settings.
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <!-- TG SMS Server Settings -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-server"></i> TG SMS Server Configuration
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('update_settings') }}" method="POST" id="tgSmsForm">
                    <div class="mb-3">
                        <label for="tg_sms_ip" class="form-label">TG Server IP Address</label>
                        <input type="text" class="form-control" id="tg_sms_ip" name="tg_sms_ip"
                               value="{{ settings.tg_sms_ip or '*************' }}"
                               placeholder="*************" readonly>
                        <div class="form-text">IP address of your TG SMS server</div>
                    </div>

                    <div class="mb-3">
                        <label for="tg_sms_port" class="form-label">TG Server Port</label>
                        <input type="text" class="form-control" id="tg_sms_port" name="tg_sms_port"
                               value="{{ settings.tg_sms_port or '5038' }}"
                               placeholder="5038" readonly>
                    </div>

                    <div class="mb-3">
                        <label for="tg_sms_username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="tg_sms_username" name="tg_sms_username"
                               value="{{ settings.tg_sms_username or 'apiuser' }}"
                               placeholder="apiuser" readonly>
                    </div>

                    <div class="mb-3">
                        <label for="tg_sms_password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="tg_sms_password" name="tg_sms_password"
                               value="••••••••" placeholder="••••••••" readonly>
                        <div class="form-text">Password is hidden for security</div>
                    </div>

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Note:</strong> TG SMS server settings are configured via environment variables.
                        Contact your system administrator to modify these settings.
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Port Management -->
{% if current_user.has_permission('can_manage_ports') %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-hdd-network"></i> SMS Port Management
                </h5>
                <a href="{{ url_for('ports') }}" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-gear"></i> Manage Ports
                </a>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>SMS Ports:</strong> Configure and manage SMS gateway ports for sending and receiving messages.
                    Each port can be assigned to specific users and has its own rate limits.
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-primary">{{ settings.total_ports or 0 }}</h4>
                            <small class="text-muted">Total Ports</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-success">Active</h4>
                            <small class="text-muted">Port Status</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-info">1/10s</h4>
                            <small class="text-muted">Rate Limit</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Connection Test -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-wifi"></i> System Diagnostics
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <form action="{{ url_for('test_connection') }}" method="POST">
                            <button type="submit" class="btn btn-outline-success w-100">
                                <i class="bi bi-wifi"></i> Test SMS Connection
                            </button>
                        </form>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('debug') }}" class="btn btn-outline-info w-100">
                            <i class="bi bi-bug"></i> Debug Console
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('debug_database_check') }}" class="btn btn-outline-warning w-100" target="_blank">
                            <i class="bi bi-database"></i> Database Check
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API Documentation -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-book"></i> API Documentation
                </h5>
            </div>
            <div class="card-body">
                <h6>Outgoing SMS API Format:</h6>
                <div class="bg-light p-3 rounded mb-3">
                    <code>
                        http://[IP]/cgi/WebCGI?1500101=account=[account]&password=[password]&port=[port]&destination=[phone]&content=[message]
                    </code>
                </div>

                <h6>TG SMS Server:</h6>
                <ul class="mb-3">
                    <li><strong>Protocol:</strong> TCP connection</li>
                    <li><strong>Default Port:</strong> 5038</li>
                    <li><strong>Authentication:</strong> Username/password based</li>
                    <li><strong>Rate Limit:</strong> 1 SMS per 10 seconds per port</li>
                </ul>

                <h6>Environment Variables:</h6>
                <div class="bg-light p-3 rounded">
                    <pre><code># SMS API Configuration
SMS_API_IP=*************
SMS_API_ACCOUNT=apiuser
SMS_API_PASSWORD=apipass
SMS_API_PORT=1

# TG SMS Server Configuration
TG_SMS_IP=*************
TG_SMS_PORT=5038
TG_SMS_USERNAME=apiuser
TG_SMS_PASSWORD=apipass</code></pre>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form submission handlers
document.getElementById('smsApiForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = {
        sms_api_ip: document.getElementById('sms_api_ip').value,
        sms_api_account: document.getElementById('sms_api_account').value,
        sms_api_password: document.getElementById('sms_api_password').value,
        sms_api_port: document.getElementById('sms_api_port').value
    };

    // In a real implementation, this would send data to the backend
    console.log('SMS API Settings:', formData);

    // Show success message
    Utils.showNotification('SMS API settings saved successfully!', 'success');
});

document.getElementById('tgSmsForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = {
        tg_sms_ip: document.getElementById('tg_sms_ip').value,
        tg_sms_port: document.getElementById('tg_sms_port').value,
        tg_sms_username: document.getElementById('tg_sms_username').value,
        tg_sms_password: document.getElementById('tg_sms_password').value
    };

    // In a real implementation, this would send data to the backend
    console.log('TG SMS Settings:', formData);

    // Show success message
    Utils.showNotification('TG SMS settings saved successfully!', 'success');
});

// Connection test functions
function testSmsApi() {
    const resultsDiv = document.getElementById('connectionResults');
    resultsDiv.innerHTML = `
        <div class="alert alert-info">
            <i class="bi bi-hourglass-split"></i> Testing SMS API connection...
        </div>
    `;

    // Simulate connection test
    setTimeout(function() {
        const success = Math.random() > 0.3; // 70% success rate for demo

        if (success) {
            resultsDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i> SMS API connection successful!
                </div>
            `;
        } else {
            resultsDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-x-circle"></i> SMS API connection failed. Please check your settings.
                </div>
            `;
        }
    }, 2000);
}

function testTgConnection() {
    const resultsDiv = document.getElementById('connectionResults');
    resultsDiv.innerHTML = `
        <div class="alert alert-info">
            <i class="bi bi-hourglass-split"></i> Testing TG SMS server connection...
        </div>
    `;

    // Simulate connection test
    setTimeout(function() {
        const success = Math.random() > 0.3; // 70% success rate for demo

        if (success) {
            resultsDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i> TG SMS server connection successful!
                </div>
            `;
        } else {
            resultsDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-x-circle"></i> TG SMS server connection failed. Please check your settings.
                </div>
            `;
        }
    }, 2000);
}
</script>
{% endblock %}
