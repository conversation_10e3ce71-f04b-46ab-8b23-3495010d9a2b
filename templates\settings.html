{% extends "base.html" %}

{% block title %}Settings - SMS Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-gear"></i> Settings
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-lg-6 mb-4">
        <!-- SMS API Settings -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-cloud"></i> SMS API Configuration
                </h5>
            </div>
            <div class="card-body">
                <form id="smsApiForm">
                    <div class="mb-3">
                        <label for="sms_api_ip" class="form-label">API Server IP Address</label>
                        <input type="text" class="form-control" id="sms_api_ip" value="*************" placeholder="*************">
                        <div class="form-text">IP address of your SMS gateway server</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="sms_api_account" class="form-label">API Account</label>
                        <input type="text" class="form-control" id="sms_api_account" value="apiuser" placeholder="apiuser">
                    </div>
                    
                    <div class="mb-3">
                        <label for="sms_api_password" class="form-label">API Password</label>
                        <input type="password" class="form-control" id="sms_api_password" value="apipass" placeholder="apipass">
                    </div>
                    
                    <div class="mb-3">
                        <label for="sms_api_port" class="form-label">Default SMS Port</label>
                        <input type="text" class="form-control" id="sms_api_port" value="1" placeholder="1">
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check"></i> Save API Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <!-- TG SMS Server Settings -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-server"></i> TG SMS Server Configuration
                </h5>
            </div>
            <div class="card-body">
                <form id="tgSmsForm">
                    <div class="mb-3">
                        <label for="tg_sms_ip" class="form-label">TG Server IP Address</label>
                        <input type="text" class="form-control" id="tg_sms_ip" value="*************" placeholder="*************">
                        <div class="form-text">IP address of your TG SMS server</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="tg_sms_port" class="form-label">TG Server Port</label>
                        <input type="text" class="form-control" id="tg_sms_port" value="5038" placeholder="5038">
                    </div>
                    
                    <div class="mb-3">
                        <label for="tg_sms_username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="tg_sms_username" value="apiuser" placeholder="apiuser">
                    </div>
                    
                    <div class="mb-3">
                        <label for="tg_sms_password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="tg_sms_password" value="apipass" placeholder="apipass">
                    </div>
                    
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check"></i> Save TG Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Connection Test -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-wifi"></i> Connection Test
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <button class="btn btn-outline-primary w-100" onclick="testSmsApi()">
                            <i class="bi bi-cloud-check"></i> Test SMS API Connection
                        </button>
                    </div>
                    <div class="col-md-6 mb-3">
                        <button class="btn btn-outline-success w-100" onclick="testTgConnection()">
                            <i class="bi bi-server"></i> Test TG SMS Connection
                        </button>
                    </div>
                </div>
                <div id="connectionResults" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<!-- API Documentation -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-book"></i> API Documentation
                </h5>
            </div>
            <div class="card-body">
                <h6>Outgoing SMS API Format:</h6>
                <div class="bg-light p-3 rounded mb-3">
                    <code>
                        http://[IP]/cgi/WebCGI?1500101=account=[account]&password=[password]&port=[port]&destination=[phone]&content=[message]
                    </code>
                </div>
                
                <h6>TG SMS Server:</h6>
                <ul class="mb-3">
                    <li><strong>Protocol:</strong> TCP connection</li>
                    <li><strong>Default Port:</strong> 5038</li>
                    <li><strong>Authentication:</strong> Username/password based</li>
                    <li><strong>Rate Limit:</strong> 1 SMS per 10 seconds per port</li>
                </ul>
                
                <h6>Environment Variables:</h6>
                <div class="bg-light p-3 rounded">
                    <pre><code># SMS API Configuration
SMS_API_IP=*************
SMS_API_ACCOUNT=apiuser
SMS_API_PASSWORD=apipass
SMS_API_PORT=1

# TG SMS Server Configuration
TG_SMS_IP=*************
TG_SMS_PORT=5038
TG_SMS_USERNAME=apiuser
TG_SMS_PASSWORD=apipass</code></pre>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form submission handlers
document.getElementById('smsApiForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = {
        sms_api_ip: document.getElementById('sms_api_ip').value,
        sms_api_account: document.getElementById('sms_api_account').value,
        sms_api_password: document.getElementById('sms_api_password').value,
        sms_api_port: document.getElementById('sms_api_port').value
    };
    
    // In a real implementation, this would send data to the backend
    console.log('SMS API Settings:', formData);
    
    // Show success message
    Utils.showNotification('SMS API settings saved successfully!', 'success');
});

document.getElementById('tgSmsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = {
        tg_sms_ip: document.getElementById('tg_sms_ip').value,
        tg_sms_port: document.getElementById('tg_sms_port').value,
        tg_sms_username: document.getElementById('tg_sms_username').value,
        tg_sms_password: document.getElementById('tg_sms_password').value
    };
    
    // In a real implementation, this would send data to the backend
    console.log('TG SMS Settings:', formData);
    
    // Show success message
    Utils.showNotification('TG SMS settings saved successfully!', 'success');
});

// Connection test functions
function testSmsApi() {
    const resultsDiv = document.getElementById('connectionResults');
    resultsDiv.innerHTML = `
        <div class="alert alert-info">
            <i class="bi bi-hourglass-split"></i> Testing SMS API connection...
        </div>
    `;
    
    // Simulate connection test
    setTimeout(function() {
        const success = Math.random() > 0.3; // 70% success rate for demo
        
        if (success) {
            resultsDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i> SMS API connection successful!
                </div>
            `;
        } else {
            resultsDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-x-circle"></i> SMS API connection failed. Please check your settings.
                </div>
            `;
        }
    }, 2000);
}

function testTgConnection() {
    const resultsDiv = document.getElementById('connectionResults');
    resultsDiv.innerHTML = `
        <div class="alert alert-info">
            <i class="bi bi-hourglass-split"></i> Testing TG SMS server connection...
        </div>
    `;
    
    // Simulate connection test
    setTimeout(function() {
        const success = Math.random() > 0.3; // 70% success rate for demo
        
        if (success) {
            resultsDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i> TG SMS server connection successful!
                </div>
            `;
        } else {
            resultsDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-x-circle"></i> TG SMS server connection failed. Please check your settings.
                </div>
            `;
        }
    }, 2000);
}
</script>
{% endblock %}
