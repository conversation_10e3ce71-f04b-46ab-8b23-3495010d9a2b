#!/usr/bin/env python3
"""
Configuration checker for SMS Management System
This script checks current environment variables and configuration
"""

import os

def check_environment():
    """Check current environment variables"""
    print("=== SMS Management System Configuration Check ===")
    print()
    
    # Check SMS API environment variables
    print("SMS API Environment Variables:")
    sms_api_vars = ['SMS_API_IP', 'SMS_API_ACCOUNT', 'SMS_API_PASSWORD', 'SMS_API_PORT']
    for var in sms_api_vars:
        value = os.environ.get(var)
        if value:
            if 'PASSWORD' in var:
                print(f"  {var}: {'*' * len(value)} (hidden)")
            else:
                print(f"  {var}: {value}")
        else:
            print(f"  {var}: Not set")
    
    print()
    
    # Check TG SMS environment variables
    print("TG SMS Environment Variables:")
    tg_sms_vars = ['TG_SMS_IP', 'TG_SMS_PORT', 'TG_SMS_USERNAME', 'TG_SMS_PASSWORD']
    for var in tg_sms_vars:
        value = os.environ.get(var)
        if value:
            if 'PASSWORD' in var:
                print(f"  {var}: {'*' * len(value)} (hidden)")
            else:
                print(f"  {var}: {value}")
        else:
            print(f"  {var}: Not set")
    
    print()
    
    # Show what the defaults would be
    print("Default Configuration (if no environment variables are set):")
    print("  SMS_API_IP: *************")
    print("  SMS_API_ACCOUNT: apiuser")
    print("  SMS_API_PASSWORD: apipass")
    print("  SMS_API_PORT: 1")
    print("  TG_SMS_IP: *************")
    print("  TG_SMS_PORT: 5038")
    print("  TG_SMS_USERNAME: apiuser")
    print("  TG_SMS_PASSWORD: apipass")
    
    print()
    
    # Check if any problematic variables are set
    problematic_vars = []
    if os.environ.get('SMS_API_IP') and os.environ.get('SMS_API_IP') != '*************':
        problematic_vars.append(f"SMS_API_IP is set to {os.environ.get('SMS_API_IP')} instead of *************")
    
    if os.environ.get('TG_SMS_IP') and os.environ.get('TG_SMS_IP') != '*************':
        problematic_vars.append(f"TG_SMS_IP is set to {os.environ.get('TG_SMS_IP')} instead of *************")
    
    if problematic_vars:
        print("⚠️  ISSUES FOUND:")
        for issue in problematic_vars:
            print(f"  - {issue}")
        print()
        print("To fix these issues, you can:")
        print("1. Unset the environment variables:")
        for var in ['SMS_API_IP', 'TG_SMS_IP']:
            if os.environ.get(var):
                print(f"   set {var}=")
        print()
        print("2. Or set them to the correct values:")
        print("   set SMS_API_IP=*************")
        print("   set TG_SMS_IP=*************")
        print()
        print("3. Then restart the application")
    else:
        print("✅ Configuration looks good!")
    
    print()
    print("=" * 50)

if __name__ == '__main__':
    check_environment()
