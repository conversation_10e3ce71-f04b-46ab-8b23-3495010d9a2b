# SMS Management System for MyGoautodial

A comprehensive SMS management system with inbox, outbox, compose, and reply functionality. Built with React frontend and Python Flask backend.

## Features

### Core Functionality
- **Inbox**: View received SMS messages with search and filtering
- **Outbox**: View sent SMS messages with status tracking
- **Compose**: Send new SMS messages with phone number validation
- **Reply**: Reply to received messages
- **Dashboard**: Overview of SMS statistics and recent activity

### SMS Integration
- **Outgoing SMS**: HTTP API integration for sending messages
- **Incoming SMS**: TCP connection to TG SMS server for receiving messages
- **Port Management**: Support for multiple SMS ports
- **Status Tracking**: Real-time message status updates

### Technical Features
- Real-time message updates
- Responsive design for mobile and desktop
- Message search and filtering
- Pagination for large message lists
- Character count and SMS segment calculation
- Phone number validation
- Error handling and user feedback

## API Integration

### Outgoing SMS API
```
URL Format: http://[IP]/cgi/WebCGI?1500101=account=apiuser&password=apipass&port=[port]&destination=[phone_number]&content=[MSG]
```

### Incoming SMS (TG SMS Server)
- **Protocol**: TCP connection
- **Port**: 5038 (default)
- **Authentication**: Username/password based
- **Format**: Structured event messages

## Installation

### Prerequisites
- Python 3.8+
- Node.js 16+
- npm or yarn

### Backend Setup
1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Configure environment variables:
```bash
# SMS API Configuration
export SMS_API_IP="*************"
export SMS_API_ACCOUNT="apiuser"
export SMS_API_PASSWORD="apipass"
export SMS_API_PORT="1"

# TG SMS Server Configuration
export TG_SMS_IP="*************"
export TG_SMS_PORT="5038"
export TG_SMS_USERNAME="apiuser"
export TG_SMS_PASSWORD="apipass"
```

3. Run the Flask application:
```bash
cd backend
python app.py
```

### Frontend Setup
1. Install Node.js dependencies:
```bash
cd frontend
npm install
```

2. Start the development server:
```bash
npm run dev
```

### Full Application
To run both frontend and backend together:
```bash
npm run dev
```

## Configuration

### SMS API Settings
Configure your SMS API settings in the Settings page or via environment variables:

- **SMS API IP**: IP address of your SMS gateway
- **API Account**: Username for SMS API
- **API Password**: Password for SMS API
- **Default Port**: Default SMS port to use

### TG SMS Server Settings
Configure your TG SMS server connection:

- **TG Server IP**: IP address of TG SMS server
- **TG Server Port**: Port number (default: 5038)
- **Username**: TG SMS server username
- **Password**: TG SMS server password

## Usage

### Sending SMS
1. Navigate to the Compose page
2. Enter recipient phone number (international format recommended)
3. Type your message
4. Optionally select a specific SMS port
5. Click Send

### Viewing Messages
- **Inbox**: View all received messages
- **Outbox**: View all sent messages with status
- **Search**: Use the search bar to find specific messages
- **Filter**: Filter messages by status (sent, pending, failed, etc.)

### Message Status
- **Pending**: Message queued for sending
- **Sent**: Message sent successfully
- **Delivered**: Message delivered to recipient
- **Failed**: Message sending failed
- **Received**: Incoming message

## API Endpoints

### SMS Management
- `POST /api/sms/send` - Send SMS message
- `GET /api/sms/inbox` - Get inbox messages
- `GET /api/sms/outbox` - Get outbox messages
- `GET /api/sms/message/{id}` - Get specific message
- `GET /api/sms/stats` - Get SMS statistics

### System Management
- `GET /api/sms/ports` - Get SMS port status

## Rate Limiting

The system respects the SMS API rate limit of 1 SMS per 10 seconds per port. Messages are queued and sent accordingly.

## Security

- Input validation for phone numbers and message content
- SQL injection protection with SQLAlchemy ORM
- CORS configuration for frontend-backend communication
- Environment variable configuration for sensitive data

## Troubleshooting

### Common Issues

1. **SMS not sending**
   - Check SMS API configuration
   - Verify network connectivity to SMS gateway
   - Check SMS port status

2. **Not receiving SMS**
   - Verify TG SMS server connection
   - Check TCP port 5038 accessibility
   - Verify TG SMS server credentials

3. **Frontend not loading**
   - Check if backend is running on port 5000
   - Verify proxy configuration in vite.config.js
   - Check browser console for errors

### Logs
- Backend logs are displayed in the console
- Check browser developer tools for frontend errors
- SMS sending/receiving events are logged with timestamps

## Development

### Project Structure
```
├── backend/
│   ├── app.py              # Main Flask application
│   └── requirements.txt    # Python dependencies
├── frontend/
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── services/       # API services
│   │   └── App.jsx         # Main App component
│   ├── package.json        # Node.js dependencies
│   └── vite.config.js      # Vite configuration
├── package.json            # Root package.json
└── README.md              # This file
```

### Adding Features
1. Backend: Add new routes in `backend/app.py`
2. Frontend: Create new components in `frontend/src/components/`
3. API: Update `frontend/src/services/api.js` for new endpoints

## License

MIT License - see LICENSE file for details.

## Support

For support and questions, please refer to the MyGoautodial documentation or contact your system administrator.
