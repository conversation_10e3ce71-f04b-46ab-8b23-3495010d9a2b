{% extends "base.html" %}

{% block title %}Compose SMS - SMS Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-pencil-square"></i> Compose SMS
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- SMS Compose Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-envelope"></i>
                    {% if reply_to %}
                        Reply to {{ phone_number }}
                    {% else %}
                        New SMS Message
                    {% endif %}
                </h5>
                {% if original_message %}
                    <small class="text-muted">
                        Replying to message from {{ original_message.created_at.strftime('%Y-%m-%d %H:%M') }}
                    </small>
                {% endif %}
            </div>

            {% if original_message %}
            <!-- Original Message Display -->
            <div class="card-body bg-light border-bottom">
                <h6 class="text-muted mb-2">
                    <i class="bi bi-chat-quote"></i> Original Message:
                </h6>
                <div class="border-start border-primary border-3 ps-3">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <strong class="text-primary">{{ original_message.phone_number }}</strong>
                        <small class="text-muted">{{ original_message.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                    </div>
                    <p class="mb-0">{{ original_message.content }}</p>
                </div>
            </div>
            {% endif %}
            <div class="card-body">
                <form action="{{ url_for('send_sms') }}" method="POST" id="smsForm">
                    {% if reply_to %}
                        <input type="hidden" name="reply_to_id" value="{{ reply_to }}">
                    {% endif %}

                    <!-- Phone Number -->
                    <div class="mb-3">
                        <label for="phone_number" class="form-label">
                            <i class="bi bi-telephone"></i> Recipient Phone Number *
                        </label>
                        <input type="tel"
                               class="form-control"
                               id="phone_number"
                               name="phone_number"
                               value="{{ phone_number }}"
                               placeholder="+1234567890"
                               required>
                        <div class="form-text">
                            Enter phone number in international format (e.g., +1234567890)
                        </div>
                    </div>

                    <!-- SMS Port Selection -->
                    <div class="mb-3">
                        <label for="port" class="form-label">
                            <i class="bi bi-router"></i> SMS Port (Optional)
                        </label>
                        <select class="form-select" id="port" name="port">
                            <option value="">Auto-select port</option>
                            {% for port in ports %}
                                <option value="{{ port.port_number }}">
                                    Port {{ port.port_number }} - {{ port.network_name or 'Unknown' }}
                                    {% if port.status and 'Up' in port.status %}(Active){% else %}(Inactive){% endif %}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Message Content -->
                    <div class="mb-3">
                        <label for="message" class="form-label">
                            <i class="bi bi-chat-text"></i> Message Content *
                        </label>
                        <textarea class="form-control"
                                  id="message"
                                  name="message"
                                  rows="6"
                                  placeholder="Type your message here..."
                                  required
                                  maxlength="1024"></textarea>
                        <div class="form-text d-flex justify-content-between">
                            <span>Maximum 1024 characters</span>
                            <span>
                                <span id="charCount">0</span> characters
                                | <span id="smsCount">1</span> SMS
                            </span>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-send"></i> Send SMS
                        </button>
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- SMS Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle"></i> SMS Information
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary mb-0" id="displayCharCount">0</h4>
                            <small class="text-muted">Characters</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-0" id="displaySmsCount">1</h4>
                        <small class="text-muted">SMS Parts</small>
                    </div>
                </div>

                <hr>

                <div class="small text-muted">
                    <div class="mb-2">
                        <strong>SMS Limits:</strong>
                    </div>
                    <ul class="mb-0">
                        <li>Single SMS: 160 characters</li>
                        <li>Long SMS: Up to 1024 characters</li>
                        <li>Long messages split into 153-char segments</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Tips -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-lightbulb"></i> Tips
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <ul class="mb-0">
                        <li>Use international format for phone numbers</li>
                        <li>Keep messages under 160 characters for single SMS</li>
                        <li>Special characters may affect character count</li>
                        <li>Check port status before sending</li>
                        <li>Long messages will be split automatically</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const messageTextarea = document.getElementById('message');
    const charCountSpan = document.getElementById('charCount');
    const smsCountSpan = document.getElementById('smsCount');
    const displayCharCount = document.getElementById('displayCharCount');
    const displaySmsCount = document.getElementById('displaySmsCount');

    function updateCounts() {
        const length = messageTextarea.value.length;
        const smsCount = length <= 160 ? 1 : Math.ceil(length / 153);

        charCountSpan.textContent = length;
        smsCountSpan.textContent = smsCount;
        displayCharCount.textContent = length;
        displaySmsCount.textContent = smsCount;

        // Update colors based on length
        if (length > 1024) {
            charCountSpan.className = 'text-danger';
            displayCharCount.className = 'text-danger mb-0';
        } else if (length > 160) {
            charCountSpan.className = 'text-warning';
            displayCharCount.className = 'text-warning mb-0';
        } else {
            charCountSpan.className = '';
            displayCharCount.className = 'text-primary mb-0';
        }

        if (smsCount > 1) {
            smsCountSpan.className = 'text-warning';
            displaySmsCount.className = 'text-warning mb-0';
        } else {
            smsCountSpan.className = '';
            displaySmsCount.className = 'text-success mb-0';
        }
    }

    messageTextarea.addEventListener('input', updateCounts);
    updateCounts(); // Initial count

    // Phone number validation
    const phoneInput = document.getElementById('phone_number');
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/[^\d+]/g, '');
        if (value && !value.startsWith('+')) {
            value = '+' + value;
        }
        this.value = value;
    });

    // Form validation
    document.getElementById('smsForm').addEventListener('submit', function(e) {
        const phoneNumber = phoneInput.value.trim();
        const message = messageTextarea.value.trim();

        if (!phoneNumber || !message) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            return;
        }

        if (phoneNumber.length < 8) {
            e.preventDefault();
            alert('Please enter a valid phone number.');
            return;
        }

        if (message.length > 1024) {
            e.preventDefault();
            alert('Message is too long. Maximum 1024 characters allowed.');
            return;
        }

        // Confirm sending
        const smsCount = message.length <= 160 ? 1 : Math.ceil(message.length / 153);
        const confirmMsg = `Send SMS to ${phoneNumber}?\n\nMessage: ${message.substring(0, 50)}${message.length > 50 ? '...' : ''}\nSMS parts: ${smsCount}`;

        if (!confirm(confirmMsg)) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
