#!/usr/bin/env python3
"""
Standalone Yeastar SMS Receiver Test
Based on your reference code - tests connection and SMS receiving independently
"""

import socket
import urllib.parse
import time

# Configuration (matching your setup)
YEASTAR_IP = '*************'
YEASTAR_PORT = 5038
USERNAME = 'apiuser'
PASSWORD = 'apipass'

def send_command(sock, command):
    """Send command to Yeastar and wait for complete response"""
    print(f"Sending command: {command.strip()}")
    sock.sendall(command.encode())
    data = b""
    while True:
        part = sock.recv(4096)
        if not part:
            break
        data += part
        if b"--END" in data:
            break
    response = data.decode(errors='ignore')
    print(f"Received response: {response}")
    return response

def login(sock):
    """Login to Yeastar TG SMS server"""
    print(f"Attempting to login to Yeastar at {YEASTAR_IP}:{YEASTAR_PORT}")

    login_cmd = (
        "Action: Login\r\n"
        f"Username: {USERNAME}\r\n"
        f"Secret: {PASSWORD}\r\n\r\n"
    )

    response = send_command(sock, login_cmd)

    if "Response: Success" in response:
        print("✅ Login successful")
        return True
    else:
        print(f"❌ Login failed: {response}")
        return False

def listen_for_sms(sock):
    """Listen for incoming SMS events"""
    print("📱 Listening for incoming SMS events...")
    print("Send an SMS to your GSM number to test receiving...")
    print("Press Ctrl+C to stop")
    print("-" * 50)

    buffer = ""

    try:
        while True:
            data = sock.recv(4096).decode(errors='ignore')
            buffer += data

            # Check if a full event block is received (ends with --END SMS EVENT--)
            while "--END SMS EVENT--" in buffer:
                event, buffer = buffer.split("--END SMS EVENT--", 1)

                if "Event: ReceivedSMS" in event:
                    print("📨 NEW SMS RECEIVED!")
                    print("=" * 50)

                    # Parse event key-values
                    lines = event.strip().splitlines()
                    sms_info = {}

                    for line in lines:
                        if ": " in line:
                            key, val = line.split(": ", 1)
                            sms_info[key.strip()] = val.strip()

                    # Display SMS information
                    sender = sms_info.get('Sender', 'Unknown')
                    recvtime = sms_info.get('Recvtime', 'Unknown')
                    gsm_port = sms_info.get('GsmPort', 'Unknown')

                    print(f"From: {sender}")
                    print(f"Time: {recvtime}")
                    print(f"GSM Port: {gsm_port}")

                    # Content is URL encoded, decode it
                    content_encoded = sms_info.get("Content", "")
                    content = urllib.parse.unquote(content_encoded)
                    print(f"Message: {content}")

                    # Show all fields for debugging
                    print("\nAll fields received:")
                    for key, value in sms_info.items():
                        print(f"  {key}: {value}")

                    print("=" * 50)
                    print()
                else:
                    # Other event types
                    if "Event:" in event:
                        event_type = ""
                        for line in event.strip().splitlines():
                            if line.startswith("Event:"):
                                event_type = line.split(":", 1)[1].strip()
                                break
                        print(f"📋 Other event received: {event_type}")

    except KeyboardInterrupt:
        print("\n🛑 Stopping SMS listener...")
    except Exception as e:
        print(f"❌ Error in SMS listener: {e}")

def test_connection():
    """Test connection to Yeastar without listening"""
    print("🔧 Testing connection to Yeastar TG SMS server...")
    print(f"Target: {YEASTAR_IP}:{YEASTAR_PORT}")
    print(f"Username: {USERNAME}")
    print(f"Password: {'*' * len(PASSWORD)}")
    print()

    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(10)
            print(f"Step 1: Connecting to {YEASTAR_IP}:{YEASTAR_PORT}...")
            s.connect((YEASTAR_IP, YEASTAR_PORT))
            print("✅ Step 1: TCP connection established")

            print("Step 2: Attempting authentication...")
            if login(s):
                print("✅ Step 2: Authentication successful")
                print("🔌 Connection test passed!")
                return True
            else:
                print("❌ Step 2: Authentication failed")
                return False

    except socket.timeout:
        print("❌ Connection timeout - Yeastar server not responding")
        print("Possible issues:")
        print("  - Yeastar TG SMS server is not running")
        print("  - Wrong IP address")
        print("  - Network connectivity issues")
        return False
    except ConnectionRefusedError:
        print(f"❌ Connection refused - Yeastar server at {YEASTAR_IP}:{YEASTAR_PORT} not accepting connections")
        print("Possible issues:")
        print("  - TG SMS service is not running on Yeastar")
        print("  - Wrong port number")
        print("  - Firewall blocking connection")
        return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        print(f"Error type: {type(e).__name__}")
        return False

def main():
    """Main function"""
    print("Yeastar SMS Receiver Test")
    print("=" * 30)
    print(f"Target: {YEASTAR_IP}:{YEASTAR_PORT}")
    print(f"Username: {USERNAME}")
    print()

    # First test connection
    if not test_connection():
        print("\n❌ Connection test failed. Please check:")
        print("1. Yeastar TG SMS server is running")
        print("2. IP address and port are correct")
        print("3. Username and password are correct")
        print("4. Firewall allows connection")
        return

    print("\n" + "=" * 50)
    print("Connection test successful!")
    print("Starting SMS listener...")
    print("=" * 50)

    # Start listening for SMS
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.connect((YEASTAR_IP, YEASTAR_PORT))
            if login(s):
                listen_for_sms(s)
    except Exception as e:
        print(f"❌ Error in main SMS listener: {e}")

if __name__ == "__main__":
    main()
