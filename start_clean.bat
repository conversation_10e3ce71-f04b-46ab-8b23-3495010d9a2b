@echo off
echo Starting SMS Management System with Clean Configuration...
echo.

echo Checking current configuration...
python check_config.py
echo.

echo Clearing any existing SMS environment variables...
set SMS_API_IP=
set SMS_API_ACCOUNT=
set SMS_API_PASSWORD=
set SMS_API_PORT=
set TG_SMS_IP=
set TG_SMS_PORT=
set TG_SMS_USERNAME=
set TG_SMS_PASSWORD=

echo Setting correct SMS configuration...
set SMS_API_IP=*************
set SMS_API_ACCOUNT=apiuser
set SMS_API_PASSWORD=apipass
set SMS_API_PORT=1
set TG_SMS_IP=*************
set TG_SMS_PORT=5038
set TG_SMS_USERNAME=apiuser
set TG_SMS_PASSWORD=apipass

echo.
echo Current SMS Configuration:
echo   SMS_API_IP=%SMS_API_IP%
echo   SMS_API_ACCOUNT=%SMS_API_ACCOUNT%
echo   SMS_API_PORT=%SMS_API_PORT%
echo   TG_SMS_IP=%TG_SMS_IP%
echo   TG_SMS_PORT=%TG_SMS_PORT%
echo   TG_SMS_USERNAME=%TG_SMS_USERNAME%
echo.

echo Installing Python dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo Failed to install Python dependencies
    echo.
    echo Please make sure Python is installed and added to PATH
    echo Download Python from: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo.
echo Starting the SMS Management System...
echo.
echo The application will be available at:
echo http://localhost:5000
echo.
echo Configuration debug URL:
echo http://localhost:5000/debug/config
echo.
echo Press Ctrl+C to stop the server
echo.

cd backend
python app.py

pause
