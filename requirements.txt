# SMS Management System Requirements
# Core web framework
flask==2.3.3
flask-cors==4.0.0
flask-sqlalchemy==3.0.5

# Database
sqlite3

# HTTP requests for SMS API
requests==2.31.0

# TCP socket handling for SMS receiver
socket
asyncio

# URL encoding/decoding
urllib3==2.0.4

# JSON handling
json

# Date/time handling
datetime

# Threading for concurrent operations
threading

# Configuration management
python-dotenv==1.0.0

# Logging
logging

# Data validation
marshmallow==3.20.1

# Password hashing (if authentication needed)
werkzeug==2.3.7

# Environment variables
os
sys

# Regular expressions for phone number validation
re

# Base64 encoding for message handling
base64

# Time utilities
time

# UUID for unique message IDs
uuid

# Error handling
traceback

# File operations
pathlib

# HTTP status codes
http.client

# Email validation (if needed for notifications)
email-validator==2.0.0

# Phone number validation
phonenumbers==8.13.19

# Timezone handling
pytz==2023.3

# CSV export functionality
csv

# Excel export functionality
openpyxl==3.1.2

# PDF generation for reports
reportlab==4.0.4

# Image processing (for MMS future support)
Pillow==10.0.0

# Encryption for sensitive data
cryptography==41.0.4

# Rate limiting
flask-limiter==3.5.0

# API documentation
flask-restx==1.1.0

# Background task processing
celery==5.3.1
redis==4.6.0

# Monitoring and health checks
psutil==5.9.5

# Configuration file parsing
configparser

# Network utilities
ipaddress

# String utilities
string

# Math operations
math

# Random number generation
random

# Collections for data structures
collections

# Itertools for efficient iteration
itertools

# Functools for function utilities
functools

# Operator functions
operator

# Copy utilities
copy

# Pickle for object serialization
pickle

# Gzip compression
gzip

# Shutil for file operations
shutil

# Glob for file pattern matching
glob

# Tempfile for temporary files
tempfile

# Subprocess for external commands
subprocess

# Signal handling
signal

# Multiprocessing
multiprocessing

# Queue for thread-safe operations
queue

# Weakref for weak references
weakref

# Garbage collection
gc

# Memory profiling
memory_profiler==0.61.0

# Performance monitoring
py-spy==0.3.14
