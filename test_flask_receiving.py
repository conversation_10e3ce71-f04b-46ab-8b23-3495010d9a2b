#!/usr/bin/env python3
"""
Test Flask SMS Receiving
Quick test to verify Flask app SMS receiving is working
"""

import requests
import time
import json

def test_flask_app():
    """Test if Flask app is running and SMS receiver is working"""
    print("🧪 Testing Flask SMS Receiving")
    print("=" * 40)
    
    base_url = "http://localhost:5000"
    
    # Test 1: Check if Flask app is running
    print("1. Testing Flask app connectivity...")
    try:
        response = requests.get(base_url, timeout=5)
        print(f"✅ Flask app is running (Status: {response.status_code})")
    except requests.exceptions.ConnectionError:
        print("❌ Flask app is not running")
        print("   Start with: cd backend && python app.py")
        return False
    except Exception as e:
        print(f"❌ Error connecting to Flask app: {e}")
        return False
    
    # Test 2: Check SMS receiver status
    print("\n2. Checking SMS receiver status...")
    try:
        response = requests.get(f"{base_url}/debug/sms_status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   Connected: {data.get('receiver_connected', False)}")
            print(f"   Running: {data.get('receiver_running', False)}")
            print(f"   Total received: {data.get('total_received_messages', 0)}")
            
            if data.get('receiver_connected') and data.get('receiver_running'):
                print("✅ SMS receiver is connected and running")
                return True
            else:
                print("❌ SMS receiver is not working properly")
                return False
        else:
            print(f"❌ Error getting SMS status (Status: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ Error checking SMS status: {e}")
        return False

def test_sms_parsing():
    """Test SMS parsing with sample data"""
    print("\n3. Testing SMS parsing...")
    
    try:
        response = requests.post("http://localhost:5000/debug/test_sms_receive", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ SMS parsing test successful")
                print(f"   Parsed data: {data.get('parsed_data', {})}")
                return True
            else:
                print("❌ SMS parsing test failed")
                print(f"   Error: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ SMS parsing test failed (Status: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ Error testing SMS parsing: {e}")
        return False

def check_recent_messages():
    """Check if any SMS messages have been received recently"""
    print("\n4. Checking recent messages...")
    
    try:
        response = requests.get("http://localhost:5000/api/sms/inbox", timeout=5)
        if response.status_code == 200:
            data = response.json()
            messages = data.get('messages', [])
            print(f"   Total messages in inbox: {len(messages)}")
            
            if messages:
                latest = messages[0]
                print(f"   Latest message from: {latest.get('phone_number', 'Unknown')}")
                print(f"   Content: {latest.get('content', 'No content')[:50]}...")
                print(f"   Received: {latest.get('created_at', 'Unknown time')}")
            else:
                print("   No messages in inbox yet")
            
            return True
        else:
            print(f"❌ Error getting inbox (Status: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ Error checking inbox: {e}")
        return False

def main():
    """Main test function"""
    print("Flask SMS Receiving Test")
    print("Make sure Flask app is running: cd backend && python app.py")
    print()
    
    # Run tests
    tests_passed = 0
    total_tests = 4
    
    if test_flask_app():
        tests_passed += 1
    
    if test_sms_parsing():
        tests_passed += 1
    
    if check_recent_messages():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 40)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed >= 2:
        print("✅ Flask SMS receiving appears to be working!")
        print("\nNext steps:")
        print("1. Send an SMS to your GSM number")
        print("2. Watch the Flask console for SMS events")
        print("3. Check http://localhost:5000/inbox for received messages")
        print("4. Look for logs like: 🎉 SMS EVENT FOUND!")
    else:
        print("❌ Flask SMS receiving has issues")
        print("\nTroubleshooting:")
        print("1. Make sure Flask app is running")
        print("2. Check SMS receiver connection status")
        print("3. Verify TG SMS service is working with: python test_receiving_only.py")
    
    print("\nReal-time monitoring:")
    print("- Watch Flask console output")
    print("- Visit http://localhost:5000/debug for status")
    print("- Check http://localhost:5000/inbox for messages")

if __name__ == "__main__":
    main()
