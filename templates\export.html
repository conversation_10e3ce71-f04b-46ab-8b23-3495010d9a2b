{% extends "base.html" %}

{% block title %}Export Data - SMS Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-download"></i> Export SMS Data
        </h1>
    </div>
</div>

<!-- Export Form -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-funnel"></i> Export Filters
                </h5>
            </div>
            <div class="card-body">
                <form id="exportForm" action="{{ url_for('export_messages') }}" method="GET">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="direction" class="form-label">Message Direction</label>
                            <select class="form-select" id="direction" name="direction">
                                <option value="all">All Messages</option>
                                <option value="inbound">Received Only</option>
                                <option value="outbound">Sent Only</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone_number" class="form-label">Phone Number</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="phone_number" 
                                   name="phone_number"
                                   placeholder="Filter by phone number (optional)">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" 
                                   class="form-control" 
                                   id="start_date" 
                                   name="start_date">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" 
                                   class="form-control" 
                                   id="end_date" 
                                   name="end_date">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-download me-2"></i>
                                    Export to CSV
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                                    <i class="bi bi-arrow-clockwise me-2"></i>
                                    Reset Filters
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="previewData()">
                                    <i class="bi bi-eye me-2"></i>
                                    Preview Data
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Quick Export Options -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i> Quick Export
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="quickExport('today')">
                        <i class="bi bi-calendar-day me-2"></i>
                        Today's Messages
                    </button>
                    <button class="btn btn-outline-primary" onclick="quickExport('week')">
                        <i class="bi bi-calendar-week me-2"></i>
                        This Week
                    </button>
                    <button class="btn btn-outline-primary" onclick="quickExport('month')">
                        <i class="bi bi-calendar-month me-2"></i>
                        This Month
                    </button>
                    <button class="btn btn-outline-success" onclick="quickExport('received')">
                        <i class="bi bi-inbox me-2"></i>
                        All Received
                    </button>
                    <button class="btn btn-outline-info" onclick="quickExport('sent')">
                        <i class="bi bi-send me-2"></i>
                        All Sent
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Export Info -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle"></i> Export Information
                </h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <ul class="list-unstyled mb-0">
                        <li><i class="bi bi-check-circle text-success me-1"></i> CSV format</li>
                        <li><i class="bi bi-check-circle text-success me-1"></i> UTF-8 encoding</li>
                        <li><i class="bi bi-check-circle text-success me-1"></i> All message fields included</li>
                        <li><i class="bi bi-check-circle text-success me-1"></i> Timestamps in local time</li>
                    </ul>
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-eye me-2"></i>Data Preview
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading preview...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="exportFromPreview()">
                    <i class="bi bi-download me-2"></i>Export This Data
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up"></i> Export Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center" id="exportStats">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h4 id="totalMessages">-</h4>
                                <small>Total Messages</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h4 id="sentMessages">-</h4>
                                <small>Sent Messages</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h4 id="receivedMessages">-</h4>
                                <small>Received Messages</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <h4 id="dateRange">-</h4>
                                <small>Date Range</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Load statistics on page load
document.addEventListener('DOMContentLoaded', function() {
    loadExportStats();
});

function loadExportStats() {
    fetch('/api/sms/stats')
        .then(response => response.json())
        .then(data => {
            document.getElementById('totalMessages').textContent = data.total_messages || 0;
            document.getElementById('sentMessages').textContent = data.total_sent || 0;
            document.getElementById('receivedMessages').textContent = data.total_received || 0;
            document.getElementById('dateRange').textContent = 'All Time';
        })
        .catch(error => {
            console.error('Error loading stats:', error);
        });
}

function resetForm() {
    document.getElementById('exportForm').reset();
}

function quickExport(type) {
    const form = document.getElementById('exportForm');
    const today = new Date();
    
    // Reset form first
    form.reset();
    
    switch(type) {
        case 'today':
            document.getElementById('start_date').value = today.toISOString().split('T')[0];
            document.getElementById('end_date').value = today.toISOString().split('T')[0];
            break;
        case 'week':
            const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
            document.getElementById('start_date').value = weekStart.toISOString().split('T')[0];
            document.getElementById('end_date').value = new Date().toISOString().split('T')[0];
            break;
        case 'month':
            const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
            document.getElementById('start_date').value = monthStart.toISOString().split('T')[0];
            document.getElementById('end_date').value = new Date().toISOString().split('T')[0];
            break;
        case 'received':
            document.getElementById('direction').value = 'inbound';
            break;
        case 'sent':
            document.getElementById('direction').value = 'outbound';
            break;
    }
    
    // Submit form
    form.submit();
}

function previewData() {
    const formData = new FormData(document.getElementById('exportForm'));
    const params = new URLSearchParams(formData);
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
    
    // Load preview data (you would implement this endpoint)
    fetch(`/api/sms/inbox?${params.toString()}&per_page=10`)
        .then(response => response.json())
        .then(data => {
            let html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>Direction</th><th>Phone</th><th>Content</th><th>Date</th><th>Status</th></tr></thead><tbody>';
            
            if (data.messages && data.messages.length > 0) {
                data.messages.forEach(msg => {
                    html += `<tr>
                        <td><span class="badge bg-${msg.direction === 'inbound' ? 'success' : 'primary'}">${msg.direction}</span></td>
                        <td>${msg.phone_number}</td>
                        <td>${msg.content.substring(0, 50)}${msg.content.length > 50 ? '...' : ''}</td>
                        <td>${new Date(msg.created_at).toLocaleDateString()}</td>
                        <td><span class="badge bg-secondary">${msg.status}</span></td>
                    </tr>`;
                });
            } else {
                html += '<tr><td colspan="5" class="text-center text-muted">No messages found with current filters</td></tr>';
            }
            
            html += '</tbody></table></div>';
            html += `<p class="text-muted mt-2"><small>Showing first 10 results. Total: ${data.total || 0} messages</small></p>`;
            
            document.getElementById('previewContent').innerHTML = html;
        })
        .catch(error => {
            document.getElementById('previewContent').innerHTML = 
                '<div class="alert alert-danger">Error loading preview data</div>';
        });
}

function exportFromPreview() {
    document.getElementById('exportForm').submit();
}
</script>
{% endblock %}
