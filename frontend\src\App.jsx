import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import Sidebar from './components/Sidebar';
import Dashboard from './components/Dashboard';
import Inbox from './components/Inbox';
import Outbox from './components/Outbox';
import Compose from './components/Compose';
import MessageView from './components/MessageView';
import Settings from './components/Settings';
import { smsApi } from './services/api';
import './App.css';

function App() {
  const [stats, setStats] = useState({
    total_sent: 0,
    total_received: 0,
    pending_messages: 0,
    failed_messages: 0,
    total_messages: 0
  });
  
  const [loading, setLoading] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(true);

  useEffect(() => {
    loadStats();
    // Refresh stats every 30 seconds
    const interval = setInterval(loadStats, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadStats = async () => {
    try {
      const response = await smsApi.getStats();
      setStats(response.data);
    } catch (error) {
      console.error('Error loading stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshStats = () => {
    loadStats();
  };

  return (
    <Router>
      <div className="flex h-screen bg-gray-100">
        <Toaster 
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
          }}
        />
        
        <Sidebar 
          isOpen={sidebarOpen}
          onToggle={() => setSidebarOpen(!sidebarOpen)}
          stats={stats}
        />
        
        <main className={`flex-1 overflow-hidden transition-all duration-300 ${
          sidebarOpen ? 'ml-64' : 'ml-16'
        }`}>
          <div className="h-full overflow-auto">
            <Routes>
              <Route 
                path="/" 
                element={<Dashboard stats={stats} loading={loading} />} 
              />
              <Route 
                path="/inbox" 
                element={<Inbox onStatsUpdate={refreshStats} />} 
              />
              <Route 
                path="/outbox" 
                element={<Outbox onStatsUpdate={refreshStats} />} 
              />
              <Route 
                path="/compose" 
                element={<Compose onStatsUpdate={refreshStats} />} 
              />
              <Route 
                path="/message/:messageId" 
                element={<MessageView onStatsUpdate={refreshStats} />} 
              />
              <Route 
                path="/settings" 
                element={<Settings />} 
              />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </div>
        </main>
      </div>
    </Router>
  );
}

export default App;
