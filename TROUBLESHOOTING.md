# SMS System Troubleshooting Guide

## Quick Diagnosis

Run these commands in order to identify the issue:

```cmd
# 1. Run comprehensive diagnosis
python diagnose.py

# 2. Test network connectivity
python test_network.py

# 3. Test Yeastar SMS connection
python test_yeastar_sms.py

# 4. Start the web application
start_clean.bat
```

## Step-by-Step Troubleshooting

### Step 1: Check Basic Connectivity

**Test if Yeast<PERSON> is reachable:**
```cmd
ping *************
```

**Expected result:** Should get replies like "Reply from *************: bytes=32 time<1ms TTL=64"

**If ping fails:**
- Check if Yeastar IP is correct (should be *************)
- Verify <PERSON><PERSON><PERSON> is powered on and connected to network
- Check network cables and switches

### Step 2: Check SMS API (Sending)

**Test SMS API directly:**
```cmd
curl "http://*************/cgi/WebCGI?1500101=account=apiuser&password=apipass&port=1&destination=+**********&content=test"
```

**Expected result:** Should get a response (even if authentication fails)

**If connection fails:**
- Yeastar web interface is not running
- Wrong IP address
- Firewall blocking HTTP (port 80)

### Step 3: Check TG SMS Server (Receiving)

**Test TG SMS port:**
```cmd
telnet ************* 5038
```

**Expected result:** Should connect and possibly show a prompt

**If connection fails:**
- TG SMS service is not running on Yeastar
- Wrong port (should be 5038)
- Firewall blocking port 5038

### Step 4: Check Yeastar Configuration

**On your Yeastar system, verify:**

1. **TG SMS Service is enabled:**
   - Go to Yeastar web interface
   - Check if TG SMS service is running
   - Verify it's listening on port 5038

2. **API User exists:**
   - Username: `apiuser`
   - Password: `apipass`
   - Has SMS permissions

3. **GSM Module is working:**
   - GSM module is detected
   - SIM card is inserted and working
   - Network registration is successful

### Step 5: Check Application Configuration

**Verify environment variables:**
```cmd
python check_config.py
```

**Should show:**
- SMS_API_IP = *************
- TG_SMS_IP = *************
- Correct username/password

### Step 6: Test Individual Components

**Test SMS sending only:**
```python
import requests
url = "http://*************/cgi/WebCGI?1500101=account=apiuser&password=apipass&port=1&destination=+************&content=test"
response = requests.get(url)
print(response.text)
```

**Test SMS receiving only:**
```cmd
python test_yeastar_sms.py
```

## Common Error Messages and Solutions

### "Connection refused"
**Cause:** Service not running or wrong port
**Solution:**
- Check if TG SMS service is running on Yeastar
- Verify port 5038 is correct
- Check firewall settings

### "Connection timeout"
**Cause:** Network connectivity issues
**Solution:**
- Test ping to *************
- Check network cables and configuration
- Verify IP address is correct

### "Authentication failed"
**Cause:** Wrong username/password
**Solution:**
- Verify credentials: apiuser/apipass
- Check if user exists on Yeastar
- Verify user has SMS permissions

### "HTTPConnectionPool... Max retries exceeded"
**Cause:** SMS API not responding
**Solution:**
- Check if Yeastar web interface is accessible
- Verify SMS API is enabled
- Test with curl command

### "No SMS events received"
**Cause:** TG SMS server not sending events
**Solution:**
- Check TG SMS server configuration
- Verify event format matches expected format
- Test with sample SMS

## Yeastar Configuration Checklist

### TG SMS Server Settings
- [ ] TG SMS service is enabled
- [ ] Listening on port 5038
- [ ] API user configured with SMS permissions
- [ ] Event notifications enabled

### GSM Module Settings
- [ ] GSM module detected and working
- [ ] SIM card inserted and activated
- [ ] Network registration successful
- [ ] SMS center number configured

### API Settings
- [ ] Web API enabled
- [ ] SMS API accessible via HTTP
- [ ] Correct account credentials
- [ ] SMS sending permissions enabled

## Testing Checklist

Run these tests in order:

1. [ ] `python diagnose.py` - Overall system check
2. [ ] `python test_network.py` - Network connectivity
3. [ ] `python test_yeastar_sms.py` - SMS protocol test
4. [ ] `start_clean.bat` - Start web application
5. [ ] Visit http://localhost:5000/debug - Check status
6. [ ] Send test SMS via web interface
7. [ ] Send SMS to GSM number and check if received

## Getting Help

If you're still having issues:

1. **Collect diagnostic information:**
   ```cmd
   python diagnose.py > diagnosis.txt
   ```

2. **Check application logs:**
   - Look at the console output when running the Flask app
   - Note any error messages or connection attempts

3. **Verify Yeastar logs:**
   - Check Yeastar system logs for SMS events
   - Look for TG SMS server connection attempts

4. **Test with minimal setup:**
   - Use the standalone test script first
   - Verify basic connectivity before testing full application

## Contact Information

For additional support:
- Check Yeastar documentation for TG SMS configuration
- Verify GSM module and SIM card functionality
- Test SMS sending/receiving manually on Yeastar interface
