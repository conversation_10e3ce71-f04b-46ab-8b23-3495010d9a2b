import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Send, Phone, MessageSquare, AlertCircle, CheckCircle, Loader } from 'lucide-react';
import toast from 'react-hot-toast';
import { smsApi, validatePhoneNumber, formatPhoneNumber } from '../services/api';

const Compose = ({ onStatsUpdate }) => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  const [formData, setFormData] = useState({
    phone_number: searchParams.get('to') || '',
    message: '',
    port: '',
    reply_to_id: searchParams.get('reply_to') || ''
  });
  
  const [sending, setSending] = useState(false);
  const [ports, setPorts] = useState([]);
  const [loadingPorts, setLoadingPorts] = useState(true);
  const [charCount, setCharCount] = useState(0);
  const [errors, setErrors] = useState({});

  const MAX_SMS_LENGTH = 160;
  const MAX_LONG_SMS_LENGTH = 1024;

  useEffect(() => {
    loadPorts();
  }, []);

  useEffect(() => {
    setCharCount(formData.message.length);
  }, [formData.message]);

  const loadPorts = async () => {
    try {
      setLoadingPorts(true);
      const response = await smsApi.getPorts();
      setPorts(response.data);
    } catch (error) {
      console.error('Error loading ports:', error);
      toast.error('Failed to load SMS ports');
    } finally {
      setLoadingPorts(false);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Validate phone number
    if (!formData.phone_number.trim()) {
      newErrors.phone_number = 'Phone number is required';
    } else if (!validatePhoneNumber(formData.phone_number)) {
      newErrors.phone_number = 'Please enter a valid phone number';
    }

    // Validate message
    if (!formData.message.trim()) {
      newErrors.message = 'Message content is required';
    } else if (formData.message.length > MAX_LONG_SMS_LENGTH) {
      newErrors.message = `Message is too long (max ${MAX_LONG_SMS_LENGTH} characters)`;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handlePhoneNumberChange = (e) => {
    const value = e.target.value;
    const formatted = formatPhoneNumber(value);
    setFormData(prev => ({
      ...prev,
      phone_number: formatted
    }));

    // Clear error when user starts typing
    if (errors.phone_number) {
      setErrors(prev => ({
        ...prev,
        phone_number: ''
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setSending(true);

    try {
      const payload = {
        phone_number: formData.phone_number,
        message: formData.message,
        ...(formData.port && { port: formData.port }),
        ...(formData.reply_to_id && { reply_to_id: formData.reply_to_id })
      };

      const response = await smsApi.sendSMS(payload);

      if (response.data.success) {
        toast.success('SMS sent successfully!');
        
        // Update stats
        if (onStatsUpdate) {
          onStatsUpdate();
        }

        // Reset form
        setFormData({
          phone_number: '',
          message: '',
          port: '',
          reply_to_id: ''
        });

        // Navigate to outbox
        navigate('/outbox');
      } else {
        toast.error(response.data.error || 'Failed to send SMS');
      }
    } catch (error) {
      console.error('Error sending SMS:', error);
      toast.error(error.message || 'Failed to send SMS');
    } finally {
      setSending(false);
    }
  };

  const getSMSSegments = () => {
    if (charCount <= MAX_SMS_LENGTH) {
      return 1;
    }
    return Math.ceil(charCount / 153); // Long SMS segments are 153 chars each
  };

  const getCharCountColor = () => {
    if (charCount > MAX_LONG_SMS_LENGTH) {
      return 'text-red-600';
    } else if (charCount > MAX_SMS_LENGTH) {
      return 'text-yellow-600';
    }
    return 'text-gray-500';
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 flex items-center space-x-3">
          <MessageSquare className="h-8 w-8 text-blue-600" />
          <span>Compose SMS</span>
        </h1>
        <p className="text-gray-600 mt-1">Send a new SMS message</p>
      </div>

      {/* Reply Context */}
      {formData.reply_to_id && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-blue-600" />
            <span className="text-sm font-medium text-blue-800">
              Replying to message ID: {formData.reply_to_id}
            </span>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Form */}
        <div className="lg:col-span-2">
          <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            {/* Phone Number */}
            <div className="mb-6">
              <label htmlFor="phone_number" className="block text-sm font-medium text-gray-700 mb-2">
                <Phone className="h-4 w-4 inline mr-2" />
                Recipient Phone Number
              </label>
              <input
                type="tel"
                id="phone_number"
                name="phone_number"
                value={formData.phone_number}
                onChange={handlePhoneNumberChange}
                placeholder="+1234567890"
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.phone_number ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={sending}
              />
              {errors.phone_number && (
                <p className="mt-2 text-sm text-red-600 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.phone_number}
                </p>
              )}
            </div>

            {/* SMS Port Selection */}
            <div className="mb-6">
              <label htmlFor="port" className="block text-sm font-medium text-gray-700 mb-2">
                SMS Port (Optional)
              </label>
              <select
                id="port"
                name="port"
                value={formData.port}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                disabled={sending || loadingPorts}
              >
                <option value="">Auto-select port</option>
                {ports.map((port) => (
                  <option key={port.id} value={port.port_number}>
                    Port {port.port_number} - {port.network_name || 'Unknown'} 
                    {port.status?.includes('Up') ? ' (Active)' : ' (Inactive)'}
                  </option>
                ))}
              </select>
              {loadingPorts && (
                <p className="mt-2 text-sm text-gray-500">Loading ports...</p>
              )}
            </div>

            {/* Message Content */}
            <div className="mb-6">
              <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                Message Content
              </label>
              <textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleInputChange}
                placeholder="Type your message here..."
                rows={6}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none ${
                  errors.message ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={sending}
              />
              
              {/* Character Count */}
              <div className="mt-2 flex justify-between items-center">
                <div className="text-sm text-gray-500">
                  {getSMSSegments() > 1 && (
                    <span className="mr-4">
                      {getSMSSegments()} SMS segments
                    </span>
                  )}
                </div>
                <span className={`text-sm ${getCharCountColor()}`}>
                  {charCount} / {MAX_LONG_SMS_LENGTH}
                </span>
              </div>
              
              {errors.message && (
                <p className="mt-2 text-sm text-red-600 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.message}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => navigate(-1)}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                disabled={sending}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={sending || !formData.phone_number || !formData.message}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
              >
                {sending ? (
                  <>
                    <Loader className="h-4 w-4 animate-spin" />
                    <span>Sending...</span>
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4" />
                    <span>Send SMS</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>

        {/* Sidebar Info */}
        <div className="space-y-6">
          {/* SMS Info */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">SMS Information</h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Character count:</span>
                <span className={getCharCountColor()}>{charCount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">SMS segments:</span>
                <span className="text-gray-900">{getSMSSegments()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Max length:</span>
                <span className="text-gray-900">{MAX_LONG_SMS_LENGTH}</span>
              </div>
            </div>
            
            {charCount > MAX_SMS_LENGTH && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  <AlertCircle className="h-4 w-4 inline mr-1" />
                  Long SMS will be sent as {getSMSSegments()} segments
                </p>
              </div>
            )}
          </div>

          {/* Tips */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Tips</h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• Use international format (+1234567890)</li>
              <li>• Keep messages under 160 characters for single SMS</li>
              <li>• Long messages will be split into multiple segments</li>
              <li>• Check port status before sending</li>
              <li>• Special characters may affect character count</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Compose;
