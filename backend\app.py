#!/usr/bin/env python3
"""
SMS Management System for MyGoautodial
Main Flask application with SMS sending/receiving capabilities
"""

from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import requests
import threading
import socket
import json
import urllib.parse
import os
import logging
import uuid
import re
from typing import Optional, Dict, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-here')
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///sms_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
CORS(app)

# SMS API Configuration
SMS_API_CONFIG = {
    'ip': os.environ.get('SMS_API_IP', '*************'),
    'account': os.environ.get('SMS_API_ACCOUNT', 'apiuser'),
    'password': os.environ.get('SMS_API_PASSWORD', 'apipass'),
    'port': os.environ.get('SMS_API_PORT', '1'),
}

# TG SMS Server Configuration
TG_SMS_CONFIG = {
    'ip': os.environ.get('TG_SMS_IP', '*************'),
    'port': int(os.environ.get('TG_SMS_PORT', '5038')),
    'username': os.environ.get('TG_SMS_USERNAME', 'apiuser'),
    'password': os.environ.get('TG_SMS_PASSWORD', 'apipass'),
}

# Database Models
class SMSMessage(db.Model):
    __tablename__ = 'sms_messages'
    
    id = db.Column(db.Integer, primary_key=True)
    message_id = db.Column(db.String(100), unique=True, nullable=False)
    direction = db.Column(db.String(10), nullable=False)  # 'inbound' or 'outbound'
    phone_number = db.Column(db.String(20), nullable=False)
    content = db.Column(db.Text, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, sent, delivered, failed, received
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    gsm_port = db.Column(db.String(10))
    smsc = db.Column(db.String(50))
    reply_to_id = db.Column(db.String(100))  # For tracking replies
    
    def to_dict(self):
        return {
            'id': self.id,
            'message_id': self.message_id,
            'direction': self.direction,
            'phone_number': self.phone_number,
            'content': self.content,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'gsm_port': self.gsm_port,
            'smsc': self.smsc,
            'reply_to_id': self.reply_to_id
        }

class SMSPort(db.Model):
    __tablename__ = 'sms_ports'
    
    id = db.Column(db.Integer, primary_key=True)
    port_number = db.Column(db.String(10), unique=True, nullable=False)
    status = db.Column(db.String(50))
    network_name = db.Column(db.String(100))
    signal_quality = db.Column(db.String(10))
    sim_imsi = db.Column(db.String(50))
    last_updated = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'port_number': self.port_number,
            'status': self.status,
            'network_name': self.network_name,
            'signal_quality': self.signal_quality,
            'sim_imsi': self.sim_imsi,
            'last_updated': self.last_updated.isoformat()
        }

# SMS Service Classes
class SMSSender:
    """Handles outbound SMS via HTTP API"""
    
    @staticmethod
    def send_sms(phone_number: str, message: str, port: str = None) -> Dict:
        """
        Send SMS using the HTTP API
        URL format: http://[IP]/cgi/WebCGI?1500101=account=apiuser&password=apipass&port=[port]&destination=[phone_number]&content=[MSG]
        """
        try:
            # Validate phone number
            if not SMSSender.validate_phone_number(phone_number):
                return {'success': False, 'error': 'Invalid phone number format'}
            
            # URL encode the message content
            encoded_message = urllib.parse.quote(message)
            
            # Use provided port or default
            sms_port = port or SMS_API_CONFIG['port']
            
            # Construct API URL
            api_url = (
                f"http://{SMS_API_CONFIG['ip']}/cgi/WebCGI?1500101="
                f"account={SMS_API_CONFIG['account']}&"
                f"password={SMS_API_CONFIG['password']}&"
                f"port={sms_port}&"
                f"destination={phone_number}&"
                f"content={encoded_message}"
            )
            
            logger.info(f"Sending SMS to {phone_number} via port {sms_port}")
            
            # Make HTTP request
            response = requests.get(api_url, timeout=30)
            
            if response.status_code == 200:
                logger.info(f"SMS sent successfully to {phone_number}")
                return {'success': True, 'response': response.text}
            else:
                logger.error(f"SMS sending failed: HTTP {response.status_code}")
                return {'success': False, 'error': f'HTTP {response.status_code}: {response.text}'}
                
        except requests.exceptions.RequestException as e:
            logger.error(f"SMS sending error: {str(e)}")
            return {'success': False, 'error': str(e)}
        except Exception as e:
            logger.error(f"Unexpected error sending SMS: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    @staticmethod
    def validate_phone_number(phone_number: str) -> bool:
        """Validate phone number format"""
        # Remove any non-digit characters except +
        cleaned = re.sub(r'[^\d+]', '', phone_number)
        
        # Check if it's a valid format (international or local)
        if re.match(r'^\+?[1-9]\d{7,14}$', cleaned):
            return True
        return False

class SMSReceiver:
    """Handles inbound SMS via TCP connection to TG SMS server"""
    
    def __init__(self):
        self.socket = None
        self.connected = False
        self.running = False
    
    def connect(self) -> bool:
        """Connect to TG SMS server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((TG_SMS_CONFIG['ip'], TG_SMS_CONFIG['port']))
            
            # Send login command
            login_cmd = (
                f"Action: Login\r\n"
                f"Username: {TG_SMS_CONFIG['username']}\r\n"
                f"Secret: {TG_SMS_CONFIG['password']}\r\n\r\n"
            )
            
            self.socket.send(login_cmd.encode())
            
            # Wait for response
            response = self.socket.recv(1024).decode()
            
            if "Response: Success" in response:
                self.connected = True
                logger.info("Successfully connected to TG SMS server")
                return True
            else:
                logger.error(f"Failed to login to TG SMS server: {response}")
                return False
                
        except Exception as e:
            logger.error(f"Error connecting to TG SMS server: {str(e)}")
            return False
    
    def start_listening(self):
        """Start listening for incoming SMS messages"""
        if not self.connected:
            if not self.connect():
                return
        
        self.running = True
        logger.info("Started listening for incoming SMS messages")
        
        try:
            while self.running:
                data = self.socket.recv(4096).decode()
                if data:
                    self.process_incoming_data(data)
                    
        except Exception as e:
            logger.error(f"Error in SMS receiver: {str(e)}")
        finally:
            self.disconnect()
    
    def process_incoming_data(self, data: str):
        """Process incoming data from TG SMS server"""
        try:
            if "Event: ReceivedSMS" in data:
                sms_data = self.parse_sms_event(data)
                if sms_data:
                    self.save_received_sms(sms_data)
                    
        except Exception as e:
            logger.error(f"Error processing incoming SMS data: {str(e)}")
    
    def parse_sms_event(self, data: str) -> Optional[Dict]:
        """Parse SMS event data"""
        try:
            lines = data.split('\n')
            sms_data = {}
            
            for line in lines:
                if ':' in line:
                    key, value = line.split(':', 1)
                    sms_data[key.strip()] = value.strip()
            
            # Extract required fields
            if all(key in sms_data for key in ['ID', 'GsmPort', 'Sender', 'Content']):
                # URL decode the content
                content = urllib.parse.unquote(sms_data['Content'])
                
                return {
                    'message_id': sms_data['ID'],
                    'gsm_port': sms_data['GsmPort'],
                    'sender': sms_data['Sender'],
                    'content': content,
                    'received_time': sms_data.get('Recvtime', ''),
                    'smsc': sms_data.get('Smsc', ''),
                    'index': sms_data.get('Index', '1'),
                    'total': sms_data.get('Total', '1')
                }
            
        except Exception as e:
            logger.error(f"Error parsing SMS event: {str(e)}")
        
        return None
    
    def save_received_sms(self, sms_data: Dict):
        """Save received SMS to database"""
        try:
            with app.app_context():
                # Check if message already exists
                existing = SMSMessage.query.filter_by(message_id=sms_data['message_id']).first()
                if existing:
                    logger.info(f"SMS message {sms_data['message_id']} already exists")
                    return
                
                # Create new SMS message
                sms_message = SMSMessage(
                    message_id=sms_data['message_id'],
                    direction='inbound',
                    phone_number=sms_data['sender'],
                    content=sms_data['content'],
                    status='received',
                    gsm_port=sms_data['gsm_port'],
                    smsc=sms_data['smsc']
                )
                
                db.session.add(sms_message)
                db.session.commit()
                
                logger.info(f"Saved received SMS from {sms_data['sender']}: {sms_data['content'][:50]}...")
                
        except Exception as e:
            logger.error(f"Error saving received SMS: {str(e)}")
    
    def disconnect(self):
        """Disconnect from TG SMS server"""
        self.running = False
        self.connected = False
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
        logger.info("Disconnected from TG SMS server")

# Global SMS receiver instance
sms_receiver = SMSReceiver()

# API Routes
@app.route('/')
def index():
    """Main dashboard"""
    return render_template('index.html')

@app.route('/api/sms/send', methods=['POST'])
def send_sms():
    """Send SMS message"""
    try:
        data = request.get_json()
        
        if not data or 'phone_number' not in data or 'message' not in data:
            return jsonify({'error': 'Missing required fields: phone_number, message'}), 400
        
        phone_number = data['phone_number']
        message = data['message']
        port = data.get('port')
        reply_to_id = data.get('reply_to_id')
        
        # Generate unique message ID
        message_id = str(uuid.uuid4())
        
        # Save to database first
        sms_message = SMSMessage(
            message_id=message_id,
            direction='outbound',
            phone_number=phone_number,
            content=message,
            status='pending',
            gsm_port=port,
            reply_to_id=reply_to_id
        )
        
        db.session.add(sms_message)
        db.session.commit()
        
        # Send SMS
        result = SMSSender.send_sms(phone_number, message, port)
        
        # Update status based on result
        if result['success']:
            sms_message.status = 'sent'
        else:
            sms_message.status = 'failed'
        
        db.session.commit()
        
        return jsonify({
            'success': result['success'],
            'message_id': message_id,
            'error': result.get('error'),
            'sms_data': sms_message.to_dict()
        })
        
    except Exception as e:
        logger.error(f"Error in send_sms: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/inbox', methods=['GET'])
def get_inbox():
    """Get inbox messages (received SMS)"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        messages = SMSMessage.query.filter_by(direction='inbound')\
                                 .order_by(SMSMessage.created_at.desc())\
                                 .paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'messages': [msg.to_dict() for msg in messages.items],
            'total': messages.total,
            'pages': messages.pages,
            'current_page': page
        })
        
    except Exception as e:
        logger.error(f"Error in get_inbox: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/outbox', methods=['GET'])
def get_outbox():
    """Get outbox messages (sent SMS)"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        messages = SMSMessage.query.filter_by(direction='outbound')\
                                 .order_by(SMSMessage.created_at.desc())\
                                 .paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'messages': [msg.to_dict() for msg in messages.items],
            'total': messages.total,
            'pages': messages.pages,
            'current_page': page
        })
        
    except Exception as e:
        logger.error(f"Error in get_outbox: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/message/<message_id>', methods=['GET'])
def get_message(message_id):
    """Get specific message details"""
    try:
        message = SMSMessage.query.filter_by(message_id=message_id).first()
        
        if not message:
            return jsonify({'error': 'Message not found'}), 404
        
        return jsonify(message.to_dict())
        
    except Exception as e:
        logger.error(f"Error in get_message: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/ports', methods=['GET'])
def get_ports():
    """Get SMS port status"""
    try:
        ports = SMSPort.query.all()
        return jsonify([port.to_dict() for port in ports])
        
    except Exception as e:
        logger.error(f"Error in get_ports: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/stats', methods=['GET'])
def get_stats():
    """Get SMS statistics"""
    try:
        total_sent = SMSMessage.query.filter_by(direction='outbound').count()
        total_received = SMSMessage.query.filter_by(direction='inbound').count()
        pending_messages = SMSMessage.query.filter_by(status='pending').count()
        failed_messages = SMSMessage.query.filter_by(status='failed').count()
        
        return jsonify({
            'total_sent': total_sent,
            'total_received': total_received,
            'pending_messages': pending_messages,
            'failed_messages': failed_messages,
            'total_messages': total_sent + total_received
        })
        
    except Exception as e:
        logger.error(f"Error in get_stats: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Initialize database
def init_db():
    """Initialize database tables"""
    with app.app_context():
        db.create_all()
        logger.info("Database initialized")

# Start SMS receiver in background thread
def start_sms_receiver():
    """Start SMS receiver in background thread"""
    def receiver_thread():
        try:
            sms_receiver.start_listening()
        except Exception as e:
            logger.error(f"SMS receiver thread error: {str(e)}")
    
    thread = threading.Thread(target=receiver_thread, daemon=True)
    thread.start()
    logger.info("SMS receiver thread started")

if __name__ == '__main__':
    # Initialize database
    init_db()
    
    # Start SMS receiver
    start_sms_receiver()
    
    # Run Flask app
    app.run(
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 5000)),
        debug=os.environ.get('DEBUG', 'False').lower() == 'true'
    )
