#!/usr/bin/env python3
"""
SMS Management System for MyGoautodial
Flask application with HTML/CSS frontend
"""

from flask import Flask, request, jsonify, render_template, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import requests
import threading
import socket
import json
import urllib.parse
import os
import logging
import uuid
import re
import time
from typing import Optional, Dict, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__, template_folder='../templates', static_folder='../static')
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-here-change-this')
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///sms_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)

# SMS API Configuration
SMS_API_CONFIG = {
    'ip': os.environ.get('SMS_API_IP', '*************'),
    'account': os.environ.get('SMS_API_ACCOUNT', 'apiuser'),
    'password': os.environ.get('SMS_API_PASSWORD', 'apipass'),
    'port': os.environ.get('SMS_API_PORT', '1'),
}

# TG SMS Server Configuration
TG_SMS_CONFIG = {
    'ip': os.environ.get('TG_SMS_IP', '*************'),
    'port': int(os.environ.get('TG_SMS_PORT', '5038')),
    'username': os.environ.get('TG_SMS_USERNAME', 'apiuser'),
    'password': os.environ.get('TG_SMS_PASSWORD', 'apipass'),
}

# Database Models
class SMSMessage(db.Model):
    __tablename__ = 'sms_messages'

    id = db.Column(db.Integer, primary_key=True)
    message_id = db.Column(db.String(100), unique=True, nullable=False)
    direction = db.Column(db.String(10), nullable=False)  # 'inbound' or 'outbound'
    phone_number = db.Column(db.String(20), nullable=False)
    content = db.Column(db.Text, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, sent, delivered, failed, received
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    gsm_port = db.Column(db.String(10))
    smsc = db.Column(db.String(50))
    reply_to_id = db.Column(db.String(100))  # For tracking replies

    def to_dict(self):
        return {
            'id': self.id,
            'message_id': self.message_id,
            'direction': self.direction,
            'phone_number': self.phone_number,
            'content': self.content,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'gsm_port': self.gsm_port,
            'smsc': self.smsc,
            'reply_to_id': self.reply_to_id
        }

class SMSPort(db.Model):
    __tablename__ = 'sms_ports'

    id = db.Column(db.Integer, primary_key=True)
    port_number = db.Column(db.String(10), unique=True, nullable=False)
    status = db.Column(db.String(50))
    network_name = db.Column(db.String(100))
    signal_quality = db.Column(db.String(10))
    sim_imsi = db.Column(db.String(50))
    last_updated = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'port_number': self.port_number,
            'status': self.status,
            'network_name': self.network_name,
            'signal_quality': self.signal_quality,
            'sim_imsi': self.sim_imsi,
            'last_updated': self.last_updated.isoformat()
        }

# SMS Service Classes
class SMSSender:
    """Handles outbound SMS via HTTP API"""

    @staticmethod
    def send_sms(phone_number: str, message: str, port: str = None) -> Dict:
        """
        Send SMS using the HTTP API
        URL format: http://[IP]/cgi/WebCGI?1500101=account=apiuser&password=apipass&port=[port]&destination=[phone_number]&content=[MSG]
        """
        try:
            # Validate phone number
            if not SMSSender.validate_phone_number(phone_number):
                return {'success': False, 'error': 'Invalid phone number format'}

            # URL encode the message content
            encoded_message = urllib.parse.quote(message)

            # Use provided port or default
            sms_port = port or SMS_API_CONFIG['port']

            # Construct API URL
            api_url = (
                f"http://{SMS_API_CONFIG['ip']}/cgi/WebCGI?1500101="
                f"account={SMS_API_CONFIG['account']}&"
                f"password={SMS_API_CONFIG['password']}&"
                f"port={sms_port}&"
                f"destination={phone_number}&"
                f"content={encoded_message}"
            )

            logger.info(f"Sending SMS to {phone_number} via port {sms_port}")
            logger.info(f"Using SMS API URL: {api_url}")

            # Make HTTP request
            response = requests.get(api_url, timeout=30)

            if response.status_code == 200:
                logger.info(f"SMS sent successfully to {phone_number}")
                return {'success': True, 'response': response.text}
            else:
                logger.error(f"SMS sending failed: HTTP {response.status_code}")
                return {'success': False, 'error': f'HTTP {response.status_code}: {response.text}'}

        except requests.exceptions.RequestException as e:
            logger.error(f"SMS sending error: {str(e)}")
            return {'success': False, 'error': str(e)}
        except Exception as e:
            logger.error(f"Unexpected error sending SMS: {str(e)}")
            return {'success': False, 'error': str(e)}

    @staticmethod
    def validate_phone_number(phone_number: str) -> bool:
        """Validate phone number format"""
        # Remove any non-digit characters except +
        cleaned = re.sub(r'[^\d+]', '', phone_number)

        # Check if it's a valid format (international or local)
        if re.match(r'^\+?[1-9]\d{7,14}$', cleaned):
            return True
        return False

class SMSReceiver:
    """Handles inbound SMS via TCP connection to TG SMS server"""

    def __init__(self):
        self.socket = None
        self.connected = False
        self.running = False
        self.buffer = ""  # Buffer for accumulating incoming data

    def send_command(self, command: str) -> str:
        """Send command to Yeastar and wait for complete response"""
        try:
            self.socket.sendall(command.encode())
            data = b""
            while True:
                part = self.socket.recv(4096)
                if not part:
                    break
                data += part
                if b"--END" in data:
                    break
            return data.decode(errors='ignore')
        except Exception as e:
            logger.error(f"Error sending command: {str(e)}")
            return ""

    def connect(self) -> bool:
        """Connect to Yeastar TG SMS server"""
        try:
            logger.info(f"Attempting to connect to Yeastar TG SMS server at {TG_SMS_CONFIG['ip']}:{TG_SMS_CONFIG['port']}")

            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)  # 10 second timeout
            self.socket.connect((TG_SMS_CONFIG['ip'], TG_SMS_CONFIG['port']))

            logger.info("TCP connection established, sending login command...")

            # Send login command using Yeastar format
            login_cmd = (
                f"Action: Login\r\n"
                f"Username: {TG_SMS_CONFIG['username']}\r\n"
                f"Secret: {TG_SMS_CONFIG['password']}\r\n\r\n"
            )

            logger.info(f"Sending login command: {login_cmd.strip()}")
            response = self.send_command(login_cmd)
            logger.info(f"Received login response: {response}")

            if "Response: Success" in response:
                self.connected = True
                logger.info("✅ Successfully connected and authenticated to Yeastar TG SMS server")
                return True
            else:
                logger.error(f"❌ Failed to login to Yeastar TG SMS server. Response: {response}")
                return False

        except socket.timeout:
            logger.error("❌ Connection timeout - Yeastar TG SMS server not responding")
            return False
        except ConnectionRefusedError:
            logger.error(f"❌ Connection refused - Yeastar TG SMS server at {TG_SMS_CONFIG['ip']}:{TG_SMS_CONFIG['port']} is not accepting connections")
            return False
        except Exception as e:
            logger.error(f"❌ Error connecting to Yeastar TG SMS server: {str(e)}")
            return False

    def start_listening(self):
        """Start listening for incoming SMS messages using Yeastar protocol - matches working test script"""
        if not self.connected:
            logger.info("Not connected, attempting to connect...")
            if not self.connect():
                logger.error("Failed to connect to Yeastar TG SMS server, SMS receiving disabled")
                return

        self.running = True
        self.buffer = ""  # Reset buffer
        logger.info("📱 Started listening for incoming SMS events from Yeastar...")
        logger.info("Send an SMS to your GSM number to test receiving...")

        try:
            while self.running:
                # Receive data from Yeastar (exactly like working test script)
                data = self.socket.recv(4096).decode(errors='ignore')

                if not data:
                    # Connection closed by server
                    logger.warning("Connection closed by Yeastar TG SMS server")
                    break

                # Add to buffer
                self.buffer += data
                logger.info(f"📨 Raw data received: {repr(data)}")

                # Process complete events (terminated by --END SMS EVENT--)
                while "--END SMS EVENT--" in self.buffer:
                    event, self.buffer = self.buffer.split("--END SMS EVENT--", 1)
                    logger.info(f"📋 Complete event received: {repr(event)}")

                    if "Event: ReceivedSMS" in event:
                        logger.info("🎉 SMS EVENT FOUND!")
                        sms_data = self.parse_yeastar_sms_event(event)
                        if sms_data:
                            self.save_received_sms(sms_data)
                            logger.info("✅ SMS saved to database successfully")
                        else:
                            logger.warning("Failed to parse Yeastar SMS event")
                    else:
                        if "Event:" in event:
                            event_type = ""
                            for line in event.strip().splitlines():
                                if line.startswith("Event:"):
                                    event_type = line.split(":", 1)[1].strip()
                                    break
                            logger.info(f"📋 Other event received: {event_type}")

                # Small delay to prevent excessive CPU usage
                time.sleep(0.1)

        except Exception as e:
            logger.error(f"Error in Yeastar SMS receiver: {str(e)}")
        finally:
            self.disconnect()

    def parse_yeastar_sms_event(self, event: str) -> Optional[Dict]:
        """Parse Yeastar SMS event data using the exact format from your reference code"""
        try:
            logger.info("Parsing Yeastar SMS event data...")

            # Parse event key-values exactly like in your reference code
            lines = event.strip().splitlines()
            sms_info = {}

            for line in lines:
                if ": " in line:
                    key, val = line.split(": ", 1)
                    sms_info[key.strip()] = val.strip()

            logger.info(f"Parsed Yeastar SMS fields: {list(sms_info.keys())}")
            logger.info(f"SMS Info: {sms_info}")

            # Extract required fields using Yeastar field names
            sender = sms_info.get('Sender', '')
            content_encoded = sms_info.get('Content', '')
            recvtime = sms_info.get('Recvtime', '')

            if not sender or not content_encoded:
                logger.error(f"Missing required Yeastar SMS fields. Sender: {sender}, Content: {content_encoded}")
                logger.error(f"Available fields: {list(sms_info.keys())}")
                return None

            # Content is URL encoded, decode it (exactly like your reference code)
            content = urllib.parse.unquote(content_encoded)

            # Generate message ID if not provided
            message_id = sms_info.get('ID', str(uuid.uuid4()))

            result = {
                'message_id': message_id,
                'gsm_port': sms_info.get('GsmPort', '1'),
                'sender': sender,
                'content': content,
                'received_time': recvtime,
                'smsc': sms_info.get('Smsc', ''),
                'index': sms_info.get('Index', '1'),
                'total': sms_info.get('Total', '1')
            }

            logger.info(f"✅ Successfully parsed Yeastar SMS:")
            logger.info(f"   From: {result['sender']}")
            logger.info(f"   Time: {result['received_time']}")
            logger.info(f"   Content: {result['content']}")
            logger.info(f"   Port: {result['gsm_port']}")

            return result

        except Exception as e:
            logger.error(f"Error parsing Yeastar SMS event: {str(e)}")
            logger.error(f"Event data: {event}")
            return None

    # Keep the old method for backward compatibility
    def process_incoming_data(self, data: str):
        """Legacy method - kept for compatibility"""
        logger.info("Using legacy SMS processing method")
        return self.parse_yeastar_sms_event(data)

    def save_received_sms(self, sms_data: Dict):
        """Save received SMS to database"""
        try:
            with app.app_context():
                # Check if message already exists
                existing = SMSMessage.query.filter_by(message_id=sms_data['message_id']).first()
                if existing:
                    logger.info(f"SMS message {sms_data['message_id']} already exists")
                    return

                # Create new SMS message
                sms_message = SMSMessage(
                    message_id=sms_data['message_id'],
                    direction='inbound',
                    phone_number=sms_data['sender'],
                    content=sms_data['content'],
                    status='received',
                    gsm_port=sms_data['gsm_port'],
                    smsc=sms_data['smsc']
                )

                db.session.add(sms_message)
                db.session.commit()

                logger.info(f"Saved received SMS from {sms_data['sender']}: {sms_data['content'][:50]}...")

        except Exception as e:
            logger.error(f"Error saving received SMS: {str(e)}")

    def disconnect(self):
        """Disconnect from TG SMS server"""
        self.running = False
        self.connected = False
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
        logger.info("Disconnected from TG SMS server")

# Global SMS receiver instance
sms_receiver = SMSReceiver()

# Web Routes
@app.route('/')
def index():
    """Main dashboard"""
    try:
        # Get statistics
        total_sent = SMSMessage.query.filter_by(direction='outbound').count()
        total_received = SMSMessage.query.filter_by(direction='inbound').count()
        pending_messages = SMSMessage.query.filter_by(status='pending').count()
        failed_messages = SMSMessage.query.filter_by(status='failed').count()

        # Get recent messages
        recent_messages = SMSMessage.query.order_by(SMSMessage.created_at.desc()).limit(10).all()

        stats = {
            'total_sent': total_sent,
            'total_received': total_received,
            'pending_messages': pending_messages,
            'failed_messages': failed_messages,
            'total_messages': total_sent + total_received
        }

        return render_template('dashboard.html', stats=stats, recent_messages=recent_messages)
    except Exception as e:
        logger.error(f"Error loading dashboard: {str(e)}")
        return render_template('dashboard.html', stats={}, recent_messages=[])

@app.route('/inbox')
def inbox():
    """Inbox page"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20

        messages = SMSMessage.query.filter_by(direction='inbound')\
                                 .order_by(SMSMessage.created_at.desc())\
                                 .paginate(page=page, per_page=per_page, error_out=False)

        return render_template('inbox.html', messages=messages)
    except Exception as e:
        logger.error(f"Error loading inbox: {str(e)}")
        return render_template('inbox.html', messages=None)

@app.route('/outbox')
def outbox():
    """Outbox page"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20

        messages = SMSMessage.query.filter_by(direction='outbound')\
                                 .order_by(SMSMessage.created_at.desc())\
                                 .paginate(page=page, per_page=per_page, error_out=False)

        return render_template('outbox.html', messages=messages)
    except Exception as e:
        logger.error(f"Error loading outbox: {str(e)}")
        return render_template('outbox.html', messages=None)

@app.route('/compose')
def compose():
    """Compose SMS page"""
    try:
        # Get available ports
        ports = SMSPort.query.all()

        # Get reply-to info if provided
        reply_to = request.args.get('reply_to')
        phone_number = request.args.get('to', '')

        return render_template('compose.html', ports=ports, reply_to=reply_to, phone_number=phone_number)
    except Exception as e:
        logger.error(f"Error loading compose page: {str(e)}")
        return render_template('compose.html', ports=[], reply_to=None, phone_number='')

@app.route('/send_sms', methods=['POST'])
def send_sms():
    """Send SMS message"""
    try:
        phone_number = request.form.get('phone_number')
        message = request.form.get('message')
        port = request.form.get('port')
        reply_to_id = request.form.get('reply_to_id')

        if not phone_number or not message:
            flash('Phone number and message are required!', 'error')
            return redirect(url_for('compose'))

        # Generate unique message ID
        message_id = str(uuid.uuid4())

        # Save to database first
        sms_message = SMSMessage(
            message_id=message_id,
            direction='outbound',
            phone_number=phone_number,
            content=message,
            status='pending',
            gsm_port=port,
            reply_to_id=reply_to_id
        )

        db.session.add(sms_message)
        db.session.commit()

        # Send SMS
        result = SMSSender.send_sms(phone_number, message, port)

        # Update status based on result
        if result['success']:
            sms_message.status = 'sent'
            flash(f'SMS sent successfully to {phone_number}!', 'success')
        else:
            sms_message.status = 'failed'
            flash(f'Failed to send SMS: {result.get("error", "Unknown error")}', 'error')

        db.session.commit()

        return redirect(url_for('outbox'))

    except Exception as e:
        logger.error(f"Error sending SMS: {str(e)}")
        flash(f'Error sending SMS: {str(e)}', 'error')
        return redirect(url_for('compose'))

@app.route('/settings')
def settings():
    """Settings page"""
    return render_template('settings.html')

@app.route('/debug')
def debug():
    """Debug page for SMS troubleshooting"""
    return render_template('debug.html')

@app.route('/debug/config')
def debug_config():
    """Debug route to check current configuration"""
    return jsonify({
        'SMS_API_CONFIG': SMS_API_CONFIG,
        'TG_SMS_CONFIG': TG_SMS_CONFIG,
        'environment_variables': {
            'SMS_API_IP': os.environ.get('SMS_API_IP', 'Not set'),
            'TG_SMS_IP': os.environ.get('TG_SMS_IP', 'Not set'),
            'SMS_API_ACCOUNT': os.environ.get('SMS_API_ACCOUNT', 'Not set'),
            'SMS_API_PASSWORD': os.environ.get('SMS_API_PASSWORD', 'Not set'),
        }
    })

@app.route('/debug/sms_status')
def debug_sms_status():
    """Debug route to check SMS receiver status"""
    return jsonify({
        'receiver_connected': sms_receiver.connected,
        'receiver_running': sms_receiver.running,
        'tg_sms_config': TG_SMS_CONFIG,
        'total_received_messages': SMSMessage.query.filter_by(direction='inbound').count()
    })

@app.route('/debug/test_sms_receive', methods=['POST'])
def debug_test_sms_receive():
    """Debug route to test SMS receiving with Yeastar sample data"""
    try:
        # Sample Yeastar SMS event data for testing (matching your reference code format)
        sample_data = """Event: ReceivedSMS
Sender: +************
Content: Hello%20World%20Test%20Message
Recvtime: 2024-01-01 12:00:00
GsmPort: 1
Smsc: +**********
ID: TEST123456
Index: 1
Total: 1
--END SMS EVENT--"""

        logger.info("Testing Yeastar SMS receive with sample data...")

        # Test the parsing directly
        event_part = sample_data.split("--END SMS EVENT--")[0]
        sms_data = sms_receiver.parse_yeastar_sms_event(event_part)

        if sms_data:
            # Save to database
            sms_receiver.save_received_sms(sms_data)

            return jsonify({
                'success': True,
                'message': 'Test SMS processing completed successfully',
                'parsed_data': sms_data,
                'sample_data': sample_data
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to parse test SMS data',
                'sample_data': sample_data
            }), 400

    except Exception as e:
        logger.error(f"Error in test SMS receive: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/debug/reconnect_sms')
def debug_reconnect_sms():
    """Debug route to reconnect to TG SMS server"""
    try:
        logger.info("Manual reconnection requested...")

        # Disconnect first
        sms_receiver.disconnect()

        # Try to reconnect
        success = sms_receiver.connect()

        return jsonify({
            'success': success,
            'connected': sms_receiver.connected,
            'message': 'Reconnection successful' if success else 'Reconnection failed'
        })

    except Exception as e:
        logger.error(f"Error in manual reconnection: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# API Routes for AJAX calls
@app.route('/api/sms/inbox', methods=['GET'])
def get_inbox():
    """Get inbox messages (received SMS)"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        messages = SMSMessage.query.filter_by(direction='inbound')\
                                 .order_by(SMSMessage.created_at.desc())\
                                 .paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'messages': [msg.to_dict() for msg in messages.items],
            'total': messages.total,
            'pages': messages.pages,
            'current_page': page
        })

    except Exception as e:
        logger.error(f"Error in get_inbox: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/outbox', methods=['GET'])
def get_outbox():
    """Get outbox messages (sent SMS)"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        messages = SMSMessage.query.filter_by(direction='outbound')\
                                 .order_by(SMSMessage.created_at.desc())\
                                 .paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'messages': [msg.to_dict() for msg in messages.items],
            'total': messages.total,
            'pages': messages.pages,
            'current_page': page
        })

    except Exception as e:
        logger.error(f"Error in get_outbox: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/message/<message_id>', methods=['GET'])
def get_message(message_id):
    """Get specific message details"""
    try:
        message = SMSMessage.query.filter_by(message_id=message_id).first()

        if not message:
            return jsonify({'error': 'Message not found'}), 404

        return jsonify(message.to_dict())

    except Exception as e:
        logger.error(f"Error in get_message: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/ports', methods=['GET'])
def get_ports():
    """Get SMS port status"""
    try:
        ports = SMSPort.query.all()
        return jsonify([port.to_dict() for port in ports])

    except Exception as e:
        logger.error(f"Error in get_ports: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/stats', methods=['GET'])
def get_stats():
    """Get SMS statistics"""
    try:
        total_sent = SMSMessage.query.filter_by(direction='outbound').count()
        total_received = SMSMessage.query.filter_by(direction='inbound').count()
        pending_messages = SMSMessage.query.filter_by(status='pending').count()
        failed_messages = SMSMessage.query.filter_by(status='failed').count()

        return jsonify({
            'total_sent': total_sent,
            'total_received': total_received,
            'pending_messages': pending_messages,
            'failed_messages': failed_messages,
            'total_messages': total_sent + total_received
        })

    except Exception as e:
        logger.error(f"Error in get_stats: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Initialize database
def init_db():
    """Initialize database tables"""
    with app.app_context():
        db.create_all()
        logger.info("Database initialized")

# Start SMS receiver in background thread
def start_sms_receiver():
    """Start SMS receiver in background thread"""
    def receiver_thread():
        try:
            sms_receiver.start_listening()
        except Exception as e:
            logger.error(f"SMS receiver thread error: {str(e)}")

    thread = threading.Thread(target=receiver_thread, daemon=True)
    thread.start()
    logger.info("SMS receiver thread started")

if __name__ == '__main__':
    # Log current configuration
    logger.info("=== SMS Management System Starting ===")
    logger.info(f"SMS API IP: {SMS_API_CONFIG['ip']}")
    logger.info(f"SMS API Account: {SMS_API_CONFIG['account']}")
    logger.info(f"SMS API Port: {SMS_API_CONFIG['port']}")
    logger.info(f"TG SMS IP: {TG_SMS_CONFIG['ip']}")
    logger.info(f"TG SMS Port: {TG_SMS_CONFIG['port']}")
    logger.info(f"TG SMS Username: {TG_SMS_CONFIG['username']}")
    logger.info("=====================================")

    # Initialize database
    init_db()

    # Test TG SMS connection before starting receiver
    logger.info("Testing TG SMS connection...")
    test_receiver = SMSReceiver()
    if test_receiver.connect():
        logger.info("✅ TG SMS connection test successful")
        test_receiver.disconnect()

        # Start SMS receiver
        start_sms_receiver()
    else:
        logger.error("❌ TG SMS connection test failed")
        logger.error("SMS receiving will not work. Check:")
        logger.error("1. TG SMS service is running on Yeastar")
        logger.error("2. Port 5038 is accessible")
        logger.error("3. Username/password are correct")
        logger.error("4. Run: python test_receiving_only.py")

    # Run Flask app
    app.run(
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 5000)),
        debug=os.environ.get('DEBUG', 'False').lower() == 'true'
    )
