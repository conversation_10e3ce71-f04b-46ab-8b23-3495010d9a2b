#!/usr/bin/env python3
"""
SMS Management System for MyGoautodial
Flask application with HTML/CSS frontend
"""

from flask import Flask, request, jsonify, render_template, redirect, url_for, flash, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import Login<PERSON>anager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import requests
import threading
import socket
import json
import urllib.parse
import os
import logging
import uuid
import re
import time
import csv
import io
from functools import wraps
from typing import Optional, Dict, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__, template_folder='../templates', static_folder='../static')
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-here-change-this')
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///sms_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)

# Flask-Login setup
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Please log in to access this page.'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# SMS API Configuration
SMS_API_CONFIG = {
    'ip': os.environ.get('SMS_API_IP', '*************'),
    'account': os.environ.get('SMS_API_ACCOUNT', 'apiuser'),
    'password': os.environ.get('SMS_API_PASSWORD', 'apipass'),
    'port': os.environ.get('SMS_API_PORT', '1'),
}

# TG SMS Server Configuration
TG_SMS_CONFIG = {
    'ip': os.environ.get('TG_SMS_IP', '*************'),
    'port': int(os.environ.get('TG_SMS_PORT', '5038')),
    'username': os.environ.get('TG_SMS_USERNAME', 'apiuser'),
    'password': os.environ.get('TG_SMS_PASSWORD', 'apipass'),
}

# Database Models
class User(UserMixin, db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), default='user')  # admin, manager, user, viewer
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    # Permissions
    can_send_sms = db.Column(db.Boolean, default=True)
    can_view_inbox = db.Column(db.Boolean, default=True)
    can_view_outbox = db.Column(db.Boolean, default=True)
    can_export = db.Column(db.Boolean, default=False)
    can_manage_users = db.Column(db.Boolean, default=False)
    can_view_reports = db.Column(db.Boolean, default=False)
    can_manage_settings = db.Column(db.Boolean, default=False)
    can_manage_ports = db.Column(db.Boolean, default=False)

    # Port assignments (comma-separated list of port numbers)
    assigned_ports = db.Column(db.Text)  # e.g., "1,2,3" for ports 1, 2, and 3

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def has_permission(self, permission):
        """Check if user has specific permission"""
        if self.role == 'admin':
            return True  # Admin has all permissions
        return getattr(self, f'can_{permission}', False)

    def get_assigned_ports(self):
        """Get list of assigned port numbers"""
        if not self.assigned_ports:
            return []
        return [port.strip() for port in self.assigned_ports.split(',') if port.strip()]

    def set_assigned_ports(self, ports):
        """Set assigned ports from list"""
        if isinstance(ports, list):
            self.assigned_ports = ','.join(str(port) for port in ports)
        else:
            self.assigned_ports = str(ports)

    def can_use_port(self, port):
        """Check if user can use specific port"""
        if self.role == 'admin':
            return True
        assigned_ports = self.get_assigned_ports()
        return str(port) in assigned_ports or not assigned_ports  # If no ports assigned, can use any

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'permissions': {
                'can_send_sms': self.can_send_sms,
                'can_view_inbox': self.can_view_inbox,
                'can_view_outbox': self.can_view_outbox,
                'can_export': self.can_export,
                'can_manage_users': self.can_manage_users,
                'can_view_reports': self.can_view_reports,
                'can_manage_settings': self.can_manage_settings
            }
        }

class SMSMessage(db.Model):
    __tablename__ = 'sms_messages'

    id = db.Column(db.Integer, primary_key=True)
    message_id = db.Column(db.String(100), unique=True, nullable=False)
    direction = db.Column(db.String(10), nullable=False)  # 'inbound' or 'outbound'
    phone_number = db.Column(db.String(20), nullable=False)
    content = db.Column(db.Text, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, sent, delivered, failed, received
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    gsm_port = db.Column(db.String(10))
    smsc = db.Column(db.String(50))
    reply_to_id = db.Column(db.String(100))  # For tracking replies

    def to_dict(self):
        return {
            'id': self.id,
            'message_id': self.message_id,
            'direction': self.direction,
            'phone_number': self.phone_number,
            'content': self.content,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'gsm_port': self.gsm_port,
            'smsc': self.smsc,
            'reply_to_id': self.reply_to_id
        }

class SMSPort(db.Model):
    __tablename__ = 'sms_ports'

    id = db.Column(db.Integer, primary_key=True)
    port_number = db.Column(db.String(10), unique=True, nullable=False)
    status = db.Column(db.String(50))
    network_name = db.Column(db.String(100))
    signal_quality = db.Column(db.String(10))
    sim_imsi = db.Column(db.String(50))
    last_updated = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'port_number': self.port_number,
            'status': self.status,
            'network_name': self.network_name,
            'signal_quality': self.signal_quality,
            'sim_imsi': self.sim_imsi,
            'last_updated': self.last_updated.isoformat()
        }

# SMS Service Classes
class SMSSender:
    """Handles outbound SMS via HTTP API"""

    @staticmethod
    def send_sms(phone_number: str, message: str, port: str = None) -> Dict:
        """
        Send SMS using the HTTP API
        URL format: http://[IP]/cgi/WebCGI?1500101=account=apiuser&password=apipass&port=[port]&destination=[phone_number]&content=[MSG]
        """
        try:
            # Validate phone number
            if not SMSSender.validate_phone_number(phone_number):
                return {'success': False, 'error': 'Invalid phone number format'}

            # URL encode the message content
            encoded_message = urllib.parse.quote(message)

            # Use provided port or default
            sms_port = port or SMS_API_CONFIG['port']

            # Construct API URL
            api_url = (
                f"http://{SMS_API_CONFIG['ip']}/cgi/WebCGI?1500101="
                f"account={SMS_API_CONFIG['account']}&"
                f"password={SMS_API_CONFIG['password']}&"
                f"port={sms_port}&"
                f"destination={phone_number}&"
                f"content={encoded_message}"
            )

            logger.info(f"Sending SMS to {phone_number} via port {sms_port}")
            logger.info(f"Using SMS API URL: {api_url}")

            # Make HTTP request
            response = requests.get(api_url, timeout=30)

            if response.status_code == 200:
                logger.info(f"SMS sent successfully to {phone_number}")
                return {'success': True, 'response': response.text}
            else:
                logger.error(f"SMS sending failed: HTTP {response.status_code}")
                return {'success': False, 'error': f'HTTP {response.status_code}: {response.text}'}

        except requests.exceptions.RequestException as e:
            logger.error(f"SMS sending error: {str(e)}")
            return {'success': False, 'error': str(e)}
        except Exception as e:
            logger.error(f"Unexpected error sending SMS: {str(e)}")
            return {'success': False, 'error': str(e)}

    @staticmethod
    def validate_phone_number(phone_number: str) -> bool:
        """Validate phone number format"""
        # Remove any non-digit characters except +
        cleaned = re.sub(r'[^\d+]', '', phone_number)

        # Check if it's a valid format (international or local)
        if re.match(r'^\+?[1-9]\d{7,14}$', cleaned):
            return True
        return False

class SMSReceiver:
    """Handles inbound SMS via TCP connection to TG SMS server"""

    def __init__(self):
        self.socket = None
        self.connected = False
        self.running = False
        self.buffer = ""  # Buffer for accumulating incoming data

    def connect(self) -> bool:
        """Connect to Yeastar TG SMS server - exactly like working test script"""
        try:
            logger.info(f"Attempting to connect to Yeastar TG SMS server at {TG_SMS_CONFIG['ip']}:{TG_SMS_CONFIG['port']}")

            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)  # 10 second timeout for connection only
            self.socket.connect((TG_SMS_CONFIG['ip'], TG_SMS_CONFIG['port']))

            logger.info("TCP connection established, sending login command...")

            # Send login command exactly like working test script
            login_cmd = (
                f"Action: Login\r\n"
                f"Username: {TG_SMS_CONFIG['username']}\r\n"
                f"Secret: {TG_SMS_CONFIG['password']}\r\n\r\n"
            )

            logger.info(f"Sending login command: {login_cmd.strip()}")

            # Send command (like working test script)
            self.socket.sendall(login_cmd.encode())

            # Wait for response with timeout (like working test script)
            self.socket.settimeout(10)
            response_data = b""

            while True:
                try:
                    part = self.socket.recv(4096)
                    if not part:
                        break
                    response_data += part
                    logger.info(f"Received response chunk: {repr(part)}")

                    # Check if we have a complete response (look for Response: line)
                    response_str = response_data.decode(errors='ignore')
                    if "Response:" in response_str:
                        break

                except socket.timeout:
                    logger.warning("Timeout waiting for login response")
                    break

            response = response_data.decode(errors='ignore')
            logger.info(f"Full login response: {repr(response)}")

            if "Response: Success" in response:
                self.connected = True
                # Remove timeout for listening phase
                self.socket.settimeout(None)
                logger.info("✅ Successfully connected and authenticated to Yeastar TG SMS server")
                return True
            else:
                logger.error(f"❌ Failed to login to Yeastar TG SMS server. Response: {response}")
                return False

        except socket.timeout:
            logger.error("❌ Connection timeout - Yeastar TG SMS server not responding")
            return False
        except ConnectionRefusedError:
            logger.error(f"❌ Connection refused - Yeastar TG SMS server at {TG_SMS_CONFIG['ip']}:{TG_SMS_CONFIG['port']} is not accepting connections")
            return False
        except Exception as e:
            logger.error(f"❌ Error connecting to Yeastar TG SMS server: {str(e)}")
            return False

    def start_listening(self):
        """Start listening for incoming SMS messages using Yeastar protocol - matches working test script"""
        if not self.connected:
            logger.info("Not connected, attempting to connect...")
            if not self.connect():
                logger.error("Failed to connect to Yeastar TG SMS server, SMS receiving disabled")
                return

        self.running = True
        self.buffer = ""  # Reset buffer
        logger.info("📱 Started listening for incoming SMS events from Yeastar...")
        logger.info("Send an SMS to your GSM number to test receiving...")

        try:
            while self.running:
                # Receive data from Yeastar (exactly like working test script)
                data = self.socket.recv(4096).decode(errors='ignore')

                if not data:
                    # Connection closed by server
                    logger.warning("Connection closed by Yeastar TG SMS server")
                    break

                # Add to buffer
                self.buffer += data
                logger.info(f"📨 Raw data received: {repr(data)}")

                # Process complete events (terminated by --END SMS EVENT--)
                while "--END SMS EVENT--" in self.buffer:
                    event, self.buffer = self.buffer.split("--END SMS EVENT--", 1)
                    logger.info(f"📋 Complete event received: {repr(event)}")

                    if "Event: ReceivedSMS" in event:
                        logger.info("🎉 SMS EVENT FOUND!")
                        sms_data = self.parse_yeastar_sms_event(event)
                        if sms_data:
                            self.save_received_sms(sms_data)
                            logger.info("✅ SMS saved to database successfully")
                        else:
                            logger.warning("Failed to parse Yeastar SMS event")
                    else:
                        if "Event:" in event:
                            event_type = ""
                            for line in event.strip().splitlines():
                                if line.startswith("Event:"):
                                    event_type = line.split(":", 1)[1].strip()
                                    break
                            logger.info(f"📋 Other event received: {event_type}")

                # Small delay to prevent excessive CPU usage
                time.sleep(0.1)

        except Exception as e:
            logger.error(f"Error in Yeastar SMS receiver: {str(e)}")
        finally:
            self.disconnect()

    def parse_yeastar_sms_event(self, event: str) -> Optional[Dict]:
        """Parse Yeastar SMS event data using the exact format from your reference code"""
        try:
            logger.info("Parsing Yeastar SMS event data...")

            # Parse event key-values exactly like in your reference code
            lines = event.strip().splitlines()
            sms_info = {}

            for line in lines:
                if ": " in line:
                    key, val = line.split(": ", 1)
                    sms_info[key.strip()] = val.strip()

            logger.info(f"Parsed Yeastar SMS fields: {list(sms_info.keys())}")
            logger.info(f"SMS Info: {sms_info}")

            # Extract required fields using Yeastar field names
            sender = sms_info.get('Sender', '')
            content_encoded = sms_info.get('Content', '')
            recvtime = sms_info.get('Recvtime', '')

            if not sender or not content_encoded:
                logger.error(f"Missing required Yeastar SMS fields. Sender: {sender}, Content: {content_encoded}")
                logger.error(f"Available fields: {list(sms_info.keys())}")
                return None

            # Content is URL encoded, decode it (exactly like your reference code)
            content = urllib.parse.unquote(content_encoded)

            # Generate message ID if not provided
            message_id = sms_info.get('ID', '')
            if not message_id or message_id.strip() == '':
                # Generate unique message ID using timestamp and sender
                import time
                timestamp = str(int(time.time() * 1000))  # milliseconds
                sender_hash = str(abs(hash(sender)))[-6:]  # last 6 digits of hash
                content_hash = str(abs(hash(content)))[-4:]  # last 4 digits of content hash
                message_id = f"SMS_{timestamp}_{sender_hash}_{content_hash}"
                logger.info(f"🆔 Generated message ID: {message_id}")

            result = {
                'message_id': message_id,
                'gsm_port': sms_info.get('GsmPort', '1'),
                'sender': sender,
                'content': content,
                'received_time': recvtime,
                'smsc': sms_info.get('Smsc', ''),
                'index': sms_info.get('Index', '1'),
                'total': sms_info.get('Total', '1')
            }

            logger.info(f"✅ Successfully parsed Yeastar SMS:")
            logger.info(f"   ID: {result['message_id']}")
            logger.info(f"   From: {result['sender']}")
            logger.info(f"   Time: {result['received_time']}")
            logger.info(f"   Content: {result['content']}")
            logger.info(f"   Port: {result['gsm_port']}")

            return result

        except Exception as e:
            logger.error(f"Error parsing Yeastar SMS event: {str(e)}")
            logger.error(f"Event data: {event}")
            return None

    # Keep the old method for backward compatibility
    def process_incoming_data(self, data: str):
        """Legacy method - kept for compatibility"""
        logger.info("Using legacy SMS processing method")
        return self.parse_yeastar_sms_event(data)

    def save_received_sms(self, sms_data: Dict):
        """Save received SMS to database"""
        try:
            logger.info(f"🔄 Attempting to save SMS to database...")
            logger.info(f"SMS Data: {sms_data}")

            with app.app_context():
                # Check if message already exists
                existing = SMSMessage.query.filter_by(message_id=sms_data['message_id']).first()
                if existing:
                    logger.warning(f"⚠️ SMS message {sms_data['message_id']} already exists in database")
                    return

                # Create new SMS message
                sms_message = SMSMessage(
                    message_id=sms_data['message_id'],
                    direction='inbound',
                    phone_number=sms_data['sender'],
                    content=sms_data['content'],
                    status='received',
                    gsm_port=sms_data['gsm_port'],
                    smsc=sms_data.get('smsc', ''),
                    created_at=datetime.utcnow()
                )

                logger.info(f"📝 Created SMS message object:")
                logger.info(f"   ID: {sms_message.message_id}")
                logger.info(f"   From: {sms_message.phone_number}")
                logger.info(f"   Content: {sms_message.content}")
                logger.info(f"   Port: {sms_message.gsm_port}")

                db.session.add(sms_message)
                db.session.commit()

                # Verify it was saved
                saved_message = SMSMessage.query.filter_by(message_id=sms_data['message_id']).first()
                if saved_message:
                    logger.info(f"✅ Successfully saved SMS to database with ID: {saved_message.id}")
                    logger.info(f"   Database ID: {saved_message.id}")
                    logger.info(f"   Message ID: {saved_message.message_id}")
                    logger.info(f"   Direction: {saved_message.direction}")
                    logger.info(f"   Phone: {saved_message.phone_number}")

                    # Check total count
                    total_received = SMSMessage.query.filter_by(direction='inbound').count()
                    logger.info(f"📊 Total received messages in database: {total_received}")
                else:
                    logger.error(f"❌ Failed to verify saved SMS in database")

        except Exception as e:
            logger.error(f"❌ Error saving received SMS: {str(e)}")
            logger.error(f"Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

            # Try to rollback
            try:
                db.session.rollback()
                logger.info("🔄 Database session rolled back")
            except:
                logger.error("❌ Failed to rollback database session")

    def disconnect(self):
        """Disconnect from TG SMS server"""
        self.running = False
        self.connected = False
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
        logger.info("Disconnected from TG SMS server")

# Global SMS receiver instance
sms_receiver = SMSReceiver()

# Permission decorators
def require_permission(permission):
    """Decorator to require specific permission"""
    def decorator(f):
        @wraps(f)
        @login_required
        def decorated_function(*args, **kwargs):
            if not current_user.has_permission(permission):
                flash(f'You do not have permission to {permission.replace("_", " ")}', 'error')
                return redirect(url_for('index'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_required(f):
    """Decorator to require admin role"""
    @wraps(f)
    @login_required
    def decorated_function(*args, **kwargs):
        if current_user.role != 'admin':
            flash('Admin access required', 'error')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

# Authentication Routes
@app.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        if not username or not password:
            flash('Username and password are required', 'error')
            return render_template('login.html')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password) and user.is_active:
            login_user(user)
            user.last_login = datetime.utcnow()
            db.session.commit()

            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('index'))
        else:
            flash('Invalid username or password', 'error')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """User logout"""
    logout_user()
    flash('You have been logged out', 'info')
    return redirect(url_for('login'))

# Web Routes
@app.route('/')
@login_required
def index():
    """Main dashboard"""
    try:
        # Build queries with port filtering
        if current_user.role == 'admin':
            # Admin sees all messages
            sent_query = SMSMessage.query.filter_by(direction='outbound')
            received_query = SMSMessage.query.filter_by(direction='inbound')
            pending_query = SMSMessage.query.filter_by(status='pending')
            failed_query = SMSMessage.query.filter_by(status='failed')
            recent_query = SMSMessage.query
        else:
            # Regular users only see messages from assigned ports
            assigned_ports = current_user.get_assigned_ports()
            if assigned_ports:
                sent_query = SMSMessage.query.filter_by(direction='outbound').filter(SMSMessage.gsm_port.in_(assigned_ports))
                received_query = SMSMessage.query.filter_by(direction='inbound').filter(SMSMessage.gsm_port.in_(assigned_ports))
                pending_query = SMSMessage.query.filter_by(status='pending').filter(SMSMessage.gsm_port.in_(assigned_ports))
                failed_query = SMSMessage.query.filter_by(status='failed').filter(SMSMessage.gsm_port.in_(assigned_ports))
                recent_query = SMSMessage.query.filter(SMSMessage.gsm_port.in_(assigned_ports))
            else:
                # If no ports assigned, show all (backward compatibility)
                sent_query = SMSMessage.query.filter_by(direction='outbound')
                received_query = SMSMessage.query.filter_by(direction='inbound')
                pending_query = SMSMessage.query.filter_by(status='pending')
                failed_query = SMSMessage.query.filter_by(status='failed')
                recent_query = SMSMessage.query

        # Get statistics
        total_sent = sent_query.count()
        total_received = received_query.count()
        pending_messages = pending_query.count()
        failed_messages = failed_query.count()

        # Get recent messages
        recent_messages = recent_query.order_by(SMSMessage.created_at.desc()).limit(10).all()

        stats = {
            'total_sent': total_sent,
            'total_received': total_received,
            'pending_messages': pending_messages,
            'failed_messages': failed_messages,
            'total_messages': total_sent + total_received
        }

        return render_template('dashboard.html', stats=stats, recent_messages=recent_messages)
    except Exception as e:
        logger.error(f"Error loading dashboard: {str(e)}")
        return render_template('dashboard.html', stats={}, recent_messages=[])

@app.route('/inbox')
@login_required
@require_permission('can_view_inbox')
def inbox():
    """Inbox page"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # Build query with port filtering
        query = SMSMessage.query.filter_by(direction='inbound')

        # Filter by assigned ports if user is not admin
        if current_user.role != 'admin':
            assigned_ports = current_user.get_assigned_ports()
            if assigned_ports:  # If user has specific port assignments
                query = query.filter(SMSMessage.gsm_port.in_(assigned_ports))

        messages = query.order_by(SMSMessage.created_at.desc())\
                       .paginate(page=page, per_page=per_page, error_out=False)

        return render_template('inbox.html', messages=messages)
    except Exception as e:
        logger.error(f"Error loading inbox: {str(e)}")
        return render_template('inbox.html', messages=None)

@app.route('/outbox')
@login_required
@require_permission('can_view_outbox')
def outbox():
    """Outbox page"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # Build query with port filtering
        query = SMSMessage.query.filter_by(direction='outbound')

        # Filter by assigned ports if user is not admin
        if current_user.role != 'admin':
            assigned_ports = current_user.get_assigned_ports()
            if assigned_ports:  # If user has specific port assignments
                query = query.filter(SMSMessage.gsm_port.in_(assigned_ports))

        messages = query.order_by(SMSMessage.created_at.desc())\
                       .paginate(page=page, per_page=per_page, error_out=False)

        return render_template('outbox.html', messages=messages)
    except Exception as e:
        logger.error(f"Error loading outbox: {str(e)}")
        return render_template('outbox.html', messages=None)

@app.route('/compose')
@login_required
@require_permission('can_send_sms')
def compose():
    """Compose SMS page"""
    try:
        # Get available ports based on user permissions
        if current_user.role == 'admin':
            # Admin can see all ports
            ports = SMSPort.query.all()
        else:
            # Regular users only see assigned ports
            assigned_ports = current_user.get_assigned_ports()
            if assigned_ports:
                ports = SMSPort.query.filter(SMSPort.port_number.in_(assigned_ports)).all()
            else:
                # If no ports assigned, show all ports (backward compatibility)
                ports = SMSPort.query.all()

        # Get reply-to info if provided
        reply_to = request.args.get('reply_to')
        phone_number = request.args.get('to', '')

        # If replying to a message, get the original message details
        original_message = None
        if reply_to:
            original_message = SMSMessage.query.filter_by(message_id=reply_to).first()
            if original_message:
                # Check if user can access this message's port
                if not current_user.can_use_port(original_message.gsm_port):
                    flash('You do not have permission to reply to this message', 'error')
                    return redirect(url_for('inbox'))

                # Use the phone number from the original message if not provided
                if not phone_number:
                    phone_number = original_message.phone_number

        return render_template('compose.html',
                             ports=ports,
                             reply_to=reply_to,
                             phone_number=phone_number,
                             original_message=original_message)
    except Exception as e:
        logger.error(f"Error loading compose page: {str(e)}")
        return render_template('compose.html', ports=[], reply_to=None, phone_number='', original_message=None)

@app.route('/send_sms', methods=['POST'])
@login_required
@require_permission('can_send_sms')
def send_sms():
    """Send SMS message"""
    try:
        phone_number = request.form.get('phone_number')
        message = request.form.get('message')
        port = request.form.get('port')
        reply_to_id = request.form.get('reply_to_id')

        if not phone_number or not message:
            flash('Phone number and message are required!', 'error')
            return redirect(url_for('compose'))

        # Check if user can use the selected port
        if port and not current_user.can_use_port(port):
            flash(f'You do not have permission to use port {port}', 'error')
            return redirect(url_for('compose'))

        # Generate unique message ID
        message_id = str(uuid.uuid4())

        # Save to database first
        sms_message = SMSMessage(
            message_id=message_id,
            direction='outbound',
            phone_number=phone_number,
            content=message,
            status='pending',
            gsm_port=port,
            reply_to_id=reply_to_id
        )

        db.session.add(sms_message)
        db.session.commit()

        # Send SMS
        result = SMSSender.send_sms(phone_number, message, port)

        # Update status based on result
        if result['success']:
            sms_message.status = 'sent'
            flash(f'SMS sent successfully to {phone_number}!', 'success')
        else:
            sms_message.status = 'failed'
            flash(f'Failed to send SMS: {result.get("error", "Unknown error")}', 'error')

        db.session.commit()

        return redirect(url_for('outbox'))

    except Exception as e:
        logger.error(f"Error sending SMS: {str(e)}")
        flash(f'Error sending SMS: {str(e)}', 'error')
        return redirect(url_for('compose'))

@app.route('/sms/<int:message_id>/delete', methods=['POST'])
@login_required
def delete_sms(message_id):
    """Delete SMS message"""
    try:
        message = SMSMessage.query.get_or_404(message_id)

        # Check permissions
        if message.direction == 'inbound' and not current_user.has_permission('view_inbox'):
            flash('You do not have permission to delete inbox messages', 'error')
            return redirect(url_for('inbox'))

        if message.direction == 'outbound' and not current_user.has_permission('view_outbox'):
            flash('You do not have permission to delete outbox messages', 'error')
            return redirect(url_for('outbox'))

        direction = message.direction
        phone_number = message.phone_number

        db.session.delete(message)
        db.session.commit()

        flash(f'Message from {phone_number} deleted successfully', 'success')

        # Redirect to appropriate page
        if direction == 'inbound':
            return redirect(url_for('inbox'))
        else:
            return redirect(url_for('outbox'))

    except Exception as e:
        logger.error(f"Error deleting SMS: {str(e)}")
        flash('Error deleting message', 'error')
        return redirect(url_for('inbox'))

@app.route('/conversation/<phone_number>')
@login_required
@require_permission('can_view_inbox')
def conversation_view(phone_number):
    """View conversation with a specific phone number"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 50

        # Build query with port filtering
        query = SMSMessage.query.filter_by(phone_number=phone_number)

        # Filter by assigned ports if user is not admin
        if current_user.role != 'admin':
            assigned_ports = current_user.get_assigned_ports()
            if assigned_ports:  # If user has specific port assignments
                query = query.filter(SMSMessage.gsm_port.in_(assigned_ports))

        # Get all messages for this phone number
        messages = query.order_by(SMSMessage.created_at.desc())\
                       .paginate(page=page, per_page=per_page, error_out=False)

        # Get conversation stats with port filtering
        stats_query = SMSMessage.query.filter_by(phone_number=phone_number)
        if current_user.role != 'admin':
            assigned_ports = current_user.get_assigned_ports()
            if assigned_ports:
                stats_query = stats_query.filter(SMSMessage.gsm_port.in_(assigned_ports))

        total_messages = stats_query.count()
        sent_count = stats_query.filter_by(direction='outbound').count()
        received_count = stats_query.filter_by(direction='inbound').count()

        stats = {
            'total_messages': total_messages,
            'sent_count': sent_count,
            'received_count': received_count
        }

        return render_template('conversation.html',
                             messages=messages,
                             phone_number=phone_number,
                             stats=stats)

    except Exception as e:
        logger.error(f"Error loading conversation: {str(e)}")
        flash('Error loading conversation', 'error')
        return redirect(url_for('inbox'))

@app.route('/settings')
@login_required
@require_permission('can_manage_settings')
def settings():
    """Settings page"""
    try:
        # Get current system settings
        settings_data = {
            'sms_api_ip': SMS_API_CONFIG.get('ip', ''),
            'sms_api_account': SMS_API_CONFIG.get('account', ''),
            'sms_api_port': SMS_API_CONFIG.get('port', ''),
            'tg_sms_ip': TG_SMS_CONFIG.get('ip', ''),
            'tg_sms_port': TG_SMS_CONFIG.get('port', ''),
            'tg_sms_username': TG_SMS_CONFIG.get('username', ''),
            'receiver_status': sms_receiver.connected if sms_receiver else False,
            'total_users': User.query.count(),
            'total_messages': SMSMessage.query.count(),
            'total_ports': SMSPort.query.count()
        }

        return render_template('settings.html', settings=settings_data)
    except Exception as e:
        logger.error(f"Error loading settings: {str(e)}")
        flash('Error loading settings', 'error')
        return render_template('settings.html', settings={})

@app.route('/settings/update', methods=['POST'])
@login_required
@require_permission('can_manage_settings')
def update_settings():
    """Update system settings"""
    try:
        # This would update system settings
        # For now, just show a message that settings would be updated
        flash('Settings update functionality would be implemented here', 'info')
        return redirect(url_for('settings'))
    except Exception as e:
        logger.error(f"Error updating settings: {str(e)}")
        flash('Error updating settings', 'error')
        return redirect(url_for('settings'))

@app.route('/settings/test_connection', methods=['POST'])
@login_required
@require_permission('can_manage_settings')
def test_connection():
    """Test SMS system connections"""
    try:
        # Test TG SMS connection
        test_result = sms_receiver.test_connection() if sms_receiver else False

        if test_result:
            flash('SMS system connection test successful', 'success')
        else:
            flash('SMS system connection test failed', 'error')

        return redirect(url_for('settings'))
    except Exception as e:
        logger.error(f"Error testing connection: {str(e)}")
        flash('Error testing connection', 'error')
        return redirect(url_for('settings'))

@app.route('/debug')
@login_required
@require_permission('can_manage_settings')
def debug():
    """Debug page for SMS troubleshooting"""
    return render_template('debug.html')

@app.route('/debug/config')
@login_required
@require_permission('can_manage_settings')
def debug_config():
    """Debug route to check current configuration"""
    return jsonify({
        'SMS_API_CONFIG': SMS_API_CONFIG,
        'TG_SMS_CONFIG': TG_SMS_CONFIG,
        'environment_variables': {
            'SMS_API_IP': os.environ.get('SMS_API_IP', 'Not set'),
            'TG_SMS_IP': os.environ.get('TG_SMS_IP', 'Not set'),
            'SMS_API_ACCOUNT': os.environ.get('SMS_API_ACCOUNT', 'Not set'),
            'SMS_API_PASSWORD': os.environ.get('SMS_API_PASSWORD', 'Not set'),
        }
    })

@app.route('/debug/sms_status')
@login_required
@require_permission('can_manage_settings')
def debug_sms_status():
    """Debug route to check SMS receiver status"""
    return jsonify({
        'receiver_connected': sms_receiver.connected,
        'receiver_running': sms_receiver.running,
        'tg_sms_config': TG_SMS_CONFIG,
        'total_received_messages': SMSMessage.query.filter_by(direction='inbound').count()
    })

@app.route('/debug/test_sms_receive', methods=['POST'])
def debug_test_sms_receive():
    """Debug route to test SMS receiving with Yeastar sample data"""
    try:
        # Sample Yeastar SMS event data for testing (matching your reference code format)
        sample_data = """Event: ReceivedSMS
Sender: +************
Content: Hello%20World%20Test%20Message
Recvtime: 2024-01-01 12:00:00
GsmPort: 1
Smsc: +**********
ID: TEST123456
Index: 1
Total: 1
--END SMS EVENT--"""

        logger.info("Testing Yeastar SMS receive with sample data...")

        # Test the parsing directly
        event_part = sample_data.split("--END SMS EVENT--")[0]
        sms_data = sms_receiver.parse_yeastar_sms_event(event_part)

        if sms_data:
            # Save to database
            sms_receiver.save_received_sms(sms_data)

            return jsonify({
                'success': True,
                'message': 'Test SMS processing completed successfully',
                'parsed_data': sms_data,
                'sample_data': sample_data
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to parse test SMS data',
                'sample_data': sample_data
            }), 400

    except Exception as e:
        logger.error(f"Error in test SMS receive: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/debug/reconnect_sms')
def debug_reconnect_sms():
    """Debug route to reconnect to TG SMS server"""
    try:
        logger.info("Manual reconnection requested...")

        # Disconnect first
        sms_receiver.disconnect()

        # Try to reconnect
        success = sms_receiver.connect()

        return jsonify({
            'success': success,
            'connected': sms_receiver.connected,
            'message': 'Reconnection successful' if success else 'Reconnection failed'
        })

    except Exception as e:
        logger.error(f"Error in manual reconnection: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/debug/database_check')
@login_required
@require_permission('can_manage_settings')
def debug_database_check():
    """Debug route to check database contents"""
    try:
        # Get all messages
        all_messages = SMSMessage.query.all()
        inbound_messages = SMSMessage.query.filter_by(direction='inbound').all()
        outbound_messages = SMSMessage.query.filter_by(direction='outbound').all()

        # Get recent messages
        recent_messages = SMSMessage.query.order_by(SMSMessage.created_at.desc()).limit(5).all()

        return jsonify({
            'total_messages': len(all_messages),
            'inbound_count': len(inbound_messages),
            'outbound_count': len(outbound_messages),
            'recent_messages': [
                {
                    'id': msg.id,
                    'message_id': msg.message_id,
                    'direction': msg.direction,
                    'phone_number': msg.phone_number,
                    'content': msg.content[:50] + '...' if len(msg.content) > 50 else msg.content,
                    'status': msg.status,
                    'created_at': msg.created_at.isoformat() if msg.created_at else None,
                    'gsm_port': msg.gsm_port
                } for msg in recent_messages
            ],
            'all_inbound_messages': [
                {
                    'id': msg.id,
                    'message_id': msg.message_id,
                    'phone_number': msg.phone_number,
                    'content': msg.content,
                    'created_at': msg.created_at.isoformat() if msg.created_at else None,
                    'gsm_port': msg.gsm_port
                } for msg in inbound_messages
            ]
        })

    except Exception as e:
        logger.error(f"Error checking database: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/debug/force_save_test_sms', methods=['POST'])
def debug_force_save_test_sms():
    """Debug route to force save a test SMS directly to database"""
    try:
        logger.info("🧪 Force saving test SMS to database...")

        # Create test SMS data
        test_sms_data = {
            'message_id': f'FORCE_TEST_{uuid.uuid4()}',
            'sender': '+**********',
            'content': 'This is a force-saved test SMS message',
            'gsm_port': '1',
            'smsc': '+**********',
            'received_time': '2024-01-01 12:00:00'
        }

        # Save using the same method
        sms_receiver.save_received_sms(test_sms_data)

        # Check if it was saved
        saved_message = SMSMessage.query.filter_by(message_id=test_sms_data['message_id']).first()

        if saved_message:
            return jsonify({
                'success': True,
                'message': 'Test SMS saved successfully',
                'saved_message': {
                    'id': saved_message.id,
                    'message_id': saved_message.message_id,
                    'phone_number': saved_message.phone_number,
                    'content': saved_message.content,
                    'direction': saved_message.direction,
                    'status': saved_message.status,
                    'created_at': saved_message.created_at.isoformat() if saved_message.created_at else None
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Test SMS was not saved to database'
            }), 500

    except Exception as e:
        logger.error(f"Error force saving test SMS: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/debug/test_inbox_query')
def debug_test_inbox_query():
    """Debug route to test inbox query without authentication"""
    try:
        logger.info("🔍 Testing inbox query directly...")

        # Test the exact same query as the inbox route
        page = 1
        per_page = 20

        messages = SMSMessage.query.filter_by(direction='inbound')\
                                 .order_by(SMSMessage.created_at.desc())\
                                 .paginate(page=page, per_page=per_page, error_out=False)

        logger.info(f"📊 Inbox query results:")
        logger.info(f"   Total messages: {messages.total}")
        logger.info(f"   Current page: {messages.page}")
        logger.info(f"   Messages on this page: {len(messages.items)}")

        # Convert messages to dict for JSON response
        message_list = []
        for msg in messages.items:
            message_list.append({
                'id': msg.id,
                'message_id': msg.message_id,
                'direction': msg.direction,
                'phone_number': msg.phone_number,
                'content': msg.content,
                'status': msg.status,
                'created_at': msg.created_at.isoformat() if msg.created_at else None,
                'gsm_port': msg.gsm_port
            })

        return jsonify({
            'success': True,
            'query_results': {
                'total': messages.total,
                'page': messages.page,
                'pages': messages.pages,
                'per_page': messages.per_page,
                'has_next': messages.has_next,
                'has_prev': messages.has_prev,
                'messages': message_list
            },
            'raw_inbound_count': SMSMessage.query.filter_by(direction='inbound').count(),
            'all_messages_count': SMSMessage.query.count()
        })

    except Exception as e:
        logger.error(f"Error testing inbox query: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@app.route('/debug/test_inbox_page')
def debug_test_inbox_page():
    """Debug route to test inbox page rendering without authentication"""
    try:
        logger.info("🔍 Testing inbox page rendering directly...")

        # Test the exact same logic as the inbox route
        page = 1
        per_page = 20

        messages = SMSMessage.query.filter_by(direction='inbound')\
                                 .order_by(SMSMessage.created_at.desc())\
                                 .paginate(page=page, per_page=per_page, error_out=False)

        logger.info(f"📊 Inbox page test results:")
        logger.info(f"   Messages object: {messages}")
        logger.info(f"   Messages.items: {messages.items}")
        logger.info(f"   Total messages: {messages.total}")
        logger.info(f"   Messages on this page: {len(messages.items)}")

        # Render the inbox template directly
        return render_template('inbox.html', messages=messages)

    except Exception as e:
        logger.error(f"Error testing inbox page: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return f"<h1>Error testing inbox page</h1><pre>{str(e)}\n\n{traceback.format_exc()}</pre>"

@app.route('/debug/check_user_permissions')
def debug_check_user_permissions():
    """Debug route to check current user permissions"""
    try:
        if current_user.is_authenticated:
            user_info = {
                'authenticated': True,
                'username': current_user.username,
                'email': current_user.email,
                'role': current_user.role,
                'is_active': current_user.is_active,
                'permissions': {
                    'can_send_sms': current_user.can_send_sms,
                    'can_view_inbox': current_user.can_view_inbox,
                    'can_view_outbox': current_user.can_view_outbox,
                    'can_export': current_user.can_export,
                    'can_manage_users': current_user.can_manage_users,
                    'can_view_reports': current_user.can_view_reports,
                    'can_manage_settings': current_user.can_manage_settings,
                    'can_manage_ports': current_user.can_manage_ports,
                },
                'has_view_inbox_permission': current_user.has_permission('view_inbox')
            }
        else:
            user_info = {
                'authenticated': False,
                'message': 'User is not logged in'
            }

        return jsonify({
            'success': True,
            'user_info': user_info
        })

    except Exception as e:
        logger.error(f"Error checking user permissions: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/debug/check_all_messages')
def debug_check_all_messages():
    """Debug route to check ALL messages in database regardless of direction"""
    try:
        # Get ALL messages
        all_messages = SMSMessage.query.order_by(SMSMessage.created_at.desc()).all()

        # Group by direction
        inbound_messages = [msg for msg in all_messages if msg.direction == 'inbound']
        outbound_messages = [msg for msg in all_messages if msg.direction == 'outbound']
        other_messages = [msg for msg in all_messages if msg.direction not in ['inbound', 'outbound']]

        # Convert to dict for JSON response
        def message_to_dict(msg):
            return {
                'id': msg.id,
                'message_id': msg.message_id,
                'direction': msg.direction,
                'phone_number': msg.phone_number,
                'content': msg.content[:100] + '...' if len(msg.content) > 100 else msg.content,
                'status': msg.status,
                'created_at': msg.created_at.isoformat() if msg.created_at else None,
                'gsm_port': msg.gsm_port,
                'smsc': msg.smsc
            }

        return jsonify({
            'success': True,
            'summary': {
                'total_messages': len(all_messages),
                'inbound_count': len(inbound_messages),
                'outbound_count': len(outbound_messages),
                'other_direction_count': len(other_messages)
            },
            'all_messages': [message_to_dict(msg) for msg in all_messages],
            'inbound_messages': [message_to_dict(msg) for msg in inbound_messages],
            'outbound_messages': [message_to_dict(msg) for msg in outbound_messages],
            'other_messages': [message_to_dict(msg) for msg in other_messages] if other_messages else []
        })

    except Exception as e:
        logger.error(f"Error checking all messages: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@app.route('/debug/test_tg_connection')
def debug_test_tg_connection():
    """Debug route to test TG SMS server connectivity"""
    try:
        import socket

        host = TG_SMS_CONFIG['host']
        port = TG_SMS_CONFIG['port']

        logger.info(f"🔍 Testing connection to TG SMS server {host}:{port}")

        # Test basic TCP connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)  # 5 second timeout

        try:
            result = sock.connect_ex((host, port))
            sock.close()

            if result == 0:
                connection_status = "✅ TCP connection successful"
                can_connect = True
            else:
                connection_status = f"❌ TCP connection failed (error code: {result})"
                can_connect = False

        except Exception as e:
            connection_status = f"❌ TCP connection error: {str(e)}"
            can_connect = False

        # Check current receiver status
        receiver_status = {
            'connected': sms_receiver.connected,
            'running': sms_receiver.running,
            'socket_exists': sms_receiver.socket is not None
        }

        return jsonify({
            'success': True,
            'tg_config': TG_SMS_CONFIG,
            'connection_test': {
                'host': host,
                'port': port,
                'status': connection_status,
                'can_connect': can_connect
            },
            'receiver_status': receiver_status,
            'recommendations': [
                "Check if Yeastar TG SMS server is running" if not can_connect else "TG SMS server is reachable",
                "Check firewall settings for port 5038" if not can_connect else "Port 5038 is accessible",
                "Verify TG SMS server configuration" if not can_connect else "Network connectivity is OK",
                "Try manual reconnection" if can_connect and not receiver_status['connected'] else "Connection status looks good"
            ]
        })

    except Exception as e:
        logger.error(f"Error testing TG connection: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

# API Routes for AJAX calls
@app.route('/api/sms/inbox', methods=['GET'])
def get_inbox():
    """Get inbox messages (received SMS)"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        messages = SMSMessage.query.filter_by(direction='inbound')\
                                 .order_by(SMSMessage.created_at.desc())\
                                 .paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'messages': [msg.to_dict() for msg in messages.items],
            'total': messages.total,
            'pages': messages.pages,
            'current_page': page
        })

    except Exception as e:
        logger.error(f"Error in get_inbox: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/outbox', methods=['GET'])
def get_outbox():
    """Get outbox messages (sent SMS)"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        messages = SMSMessage.query.filter_by(direction='outbound')\
                                 .order_by(SMSMessage.created_at.desc())\
                                 .paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'messages': [msg.to_dict() for msg in messages.items],
            'total': messages.total,
            'pages': messages.pages,
            'current_page': page
        })

    except Exception as e:
        logger.error(f"Error in get_outbox: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/message/<message_id>', methods=['GET'])
def get_message(message_id):
    """Get specific message details"""
    try:
        message = SMSMessage.query.filter_by(message_id=message_id).first()

        if not message:
            return jsonify({'error': 'Message not found'}), 404

        return jsonify(message.to_dict())

    except Exception as e:
        logger.error(f"Error in get_message: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/ports', methods=['GET'])
def get_ports():
    """Get SMS port status"""
    try:
        ports = SMSPort.query.all()
        return jsonify([port.to_dict() for port in ports])

    except Exception as e:
        logger.error(f"Error in get_ports: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/stats', methods=['GET'])
def get_stats():
    """Get SMS statistics"""
    try:
        total_sent = SMSMessage.query.filter_by(direction='outbound').count()
        total_received = SMSMessage.query.filter_by(direction='inbound').count()
        pending_messages = SMSMessage.query.filter_by(status='pending').count()
        failed_messages = SMSMessage.query.filter_by(status='failed').count()

        return jsonify({
            'total_sent': total_sent,
            'total_received': total_received,
            'pending_messages': pending_messages,
            'failed_messages': failed_messages,
            'total_messages': total_sent + total_received
        })

    except Exception as e:
        logger.error(f"Error in get_stats: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/conversation/<phone_number>')
def get_conversation(phone_number):
    """Get conversation history with a specific phone number"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 50

        messages = SMSMessage.query.filter_by(phone_number=phone_number)\
                                 .order_by(SMSMessage.created_at.desc())\
                                 .paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'success': True,
            'phone_number': phone_number,
            'messages': [msg.to_dict() for msg in messages.items],
            'pagination': {
                'page': messages.page,
                'pages': messages.pages,
                'per_page': messages.per_page,
                'total': messages.total,
                'has_next': messages.has_next,
                'has_prev': messages.has_prev
            }
        })
    except Exception as e:
        logger.error(f"Error getting conversation: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sms/stats')
@login_required
def get_sms_stats():
    """Get SMS statistics for export page"""
    try:
        total_sent = SMSMessage.query.filter_by(direction='outbound').count()
        total_received = SMSMessage.query.filter_by(direction='inbound').count()
        total_messages = total_sent + total_received

        return jsonify({
            'success': True,
            'total_messages': total_messages,
            'total_sent': total_sent,
            'total_received': total_received
        })
    except Exception as e:
        logger.error(f"Error getting SMS stats: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500



# Export and Reporting Routes
@app.route('/export')
@login_required
@require_permission('can_export')
def export_page():
    """Export page"""
    return render_template('export.html')

@app.route('/export/messages')
@login_required
@require_permission('can_export')
def export_messages():
    """Export messages to CSV"""
    try:
        # Get filter parameters
        direction = request.args.get('direction', 'all')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        phone_number = request.args.get('phone_number')

        # Build query with port filtering
        query = SMSMessage.query

        # Filter by assigned ports if user is not admin
        if current_user.role != 'admin':
            assigned_ports = current_user.get_assigned_ports()
            if assigned_ports:  # If user has specific port assignments
                query = query.filter(SMSMessage.gsm_port.in_(assigned_ports))

        if direction != 'all':
            query = query.filter_by(direction=direction)

        if start_date:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            query = query.filter(SMSMessage.created_at >= start_dt)

        if end_date:
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            end_dt = end_dt.replace(hour=23, minute=59, second=59)
            query = query.filter(SMSMessage.created_at <= end_dt)

        if phone_number:
            query = query.filter(SMSMessage.phone_number.contains(phone_number))

        messages = query.order_by(SMSMessage.created_at.desc()).all()

        # Create CSV
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow([
            'Message ID', 'Direction', 'Phone Number', 'Content',
            'Status', 'Created At', 'GSM Port', 'SMSC', 'Reply To'
        ])

        # Write data
        for msg in messages:
            writer.writerow([
                msg.message_id,
                msg.direction,
                msg.phone_number,
                msg.content,
                msg.status,
                msg.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                msg.gsm_port or '',
                msg.smsc or '',
                msg.reply_to_id or ''
            ])

        # Create response
        output.seek(0)
        filename = f"sms_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

        return app.response_class(
            output.getvalue(),
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment; filename={filename}'}
        )

    except Exception as e:
        logger.error(f"Error exporting messages: {str(e)}")
        flash('Error exporting messages', 'error')
        return redirect(url_for('export_page'))

@app.route('/reports')
@login_required
@require_permission('view_reports')
def reports():
    """Reports page"""
    try:
        # Get date range (default to last 30 days)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)

        # Get statistics
        total_sent = SMSMessage.query.filter_by(direction='outbound').count()
        total_received = SMSMessage.query.filter_by(direction='inbound').count()

        # Messages by day (last 30 days)
        daily_stats = []
        for i in range(30):
            day = start_date + timedelta(days=i)
            day_start = day.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = day.replace(hour=23, minute=59, second=59, microsecond=999999)

            sent_count = SMSMessage.query.filter(
                SMSMessage.direction == 'outbound',
                SMSMessage.created_at >= day_start,
                SMSMessage.created_at <= day_end
            ).count()

            received_count = SMSMessage.query.filter(
                SMSMessage.direction == 'inbound',
                SMSMessage.created_at >= day_start,
                SMSMessage.created_at <= day_end
            ).count()

            daily_stats.append({
                'date': day.strftime('%Y-%m-%d'),
                'sent': sent_count,
                'received': received_count
            })

        # Top contacts
        from sqlalchemy import func
        top_contacts = db.session.query(
            SMSMessage.phone_number,
            func.count(SMSMessage.id).label('message_count')
        ).group_by(SMSMessage.phone_number)\
         .order_by(func.count(SMSMessage.id).desc())\
         .limit(10).all()

        # Port usage
        port_usage = db.session.query(
            SMSMessage.gsm_port,
            func.count(SMSMessage.id).label('usage_count')
        ).filter(SMSMessage.gsm_port.isnot(None))\
         .group_by(SMSMessage.gsm_port)\
         .order_by(func.count(SMSMessage.id).desc()).all()

        return render_template('reports.html',
                             total_sent=total_sent,
                             total_received=total_received,
                             daily_stats=daily_stats,
                             top_contacts=top_contacts,
                             port_usage=port_usage)

    except Exception as e:
        logger.error(f"Error loading reports: {str(e)}")
        return render_template('reports.html',
                             total_sent=0,
                             total_received=0,
                             daily_stats=[],
                             top_contacts=[],
                             port_usage=[])

@app.route('/users')
@login_required
@require_permission('manage_users')
def users():
    """User management page"""
    try:
        users = User.query.all()
        return render_template('users.html', users=users)
    except Exception as e:
        logger.error(f"Error loading users: {str(e)}")
        return render_template('users.html', users=[])

@app.route('/users/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_user():
    """Create new user"""
    if request.method == 'POST':
        try:
            username = request.form.get('username')
            email = request.form.get('email')
            password = request.form.get('password')
            role = request.form.get('role', 'user')

            # Get permissions
            permissions = {
                'can_send_sms': 'can_send_sms' in request.form,
                'can_view_inbox': 'can_view_inbox' in request.form,
                'can_view_outbox': 'can_view_outbox' in request.form,
                'can_export': 'can_export' in request.form,
                'can_manage_users': 'can_manage_users' in request.form,
                'can_view_reports': 'can_view_reports' in request.form,
                'can_manage_settings': 'can_manage_settings' in request.form,
                'can_manage_ports': 'can_manage_ports' in request.form,
            }

            # Get assigned ports
            assigned_ports = request.form.getlist('assigned_ports')

            # Validate
            if not username or not email or not password:
                flash('Username, email, and password are required', 'error')
                return render_template('create_user.html')

            # Check if user exists
            if User.query.filter_by(username=username).first():
                flash('Username already exists', 'error')
                return render_template('create_user.html')

            if User.query.filter_by(email=email).first():
                flash('Email already exists', 'error')
                return render_template('create_user.html')

            # Create user
            user = User(
                username=username,
                email=email,
                role=role,
                **permissions
            )
            user.set_password(password)
            user.set_assigned_ports(assigned_ports)

            db.session.add(user)
            db.session.commit()

            flash(f'User {username} created successfully', 'success')
            return redirect(url_for('users'))

        except Exception as e:
            logger.error(f"Error creating user: {str(e)}")
            flash('Error creating user', 'error')

    # Get available ports for assignment
    ports = SMSPort.query.all()
    return render_template('create_user.html', ports=ports)

@app.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(user_id):
    """Edit user"""
    user = User.query.get_or_404(user_id)

    if request.method == 'POST':
        try:
            user.email = request.form.get('email')
            user.role = request.form.get('role', 'user')
            user.is_active = request.form.get('is_active') == 'true'

            # Update permissions
            user.can_send_sms = 'can_send_sms' in request.form
            user.can_view_inbox = 'can_view_inbox' in request.form
            user.can_view_outbox = 'can_view_outbox' in request.form
            user.can_export = 'can_export' in request.form
            user.can_manage_users = 'can_manage_users' in request.form
            user.can_view_reports = 'can_view_reports' in request.form
            user.can_manage_settings = 'can_manage_settings' in request.form
            user.can_manage_ports = 'can_manage_ports' in request.form

            # Update assigned ports
            assigned_ports = request.form.getlist('assigned_ports')
            user.set_assigned_ports(assigned_ports)

            db.session.commit()
            flash(f'User {user.username} updated successfully', 'success')
            return redirect(url_for('users'))

        except Exception as e:
            logger.error(f"Error updating user: {str(e)}")
            flash('Error updating user', 'error')

    ports = SMSPort.query.all()
    return render_template('edit_user.html', user=user, ports=ports)

@app.route('/users/<int:user_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_user(user_id):
    """Delete user"""
    try:
        user = User.query.get_or_404(user_id)

        # Prevent deleting admin user
        if user.username == 'admin':
            flash('Cannot delete the admin user', 'error')
            return redirect(url_for('users'))

        # Prevent deleting current user
        if user.id == current_user.id:
            flash('Cannot delete your own account', 'error')
            return redirect(url_for('users'))

        username = user.username
        db.session.delete(user)
        db.session.commit()

        flash(f'User {username} deleted successfully', 'success')

    except Exception as e:
        logger.error(f"Error deleting user: {str(e)}")
        flash('Error deleting user', 'error')

    return redirect(url_for('users'))

@app.route('/users/<int:user_id>/toggle_status', methods=['POST'])
@login_required
@admin_required
def toggle_user_status(user_id):
    """Toggle user active status"""
    try:
        user = User.query.get_or_404(user_id)

        # Prevent deactivating admin user
        if user.username == 'admin':
            flash('Cannot deactivate the admin user', 'error')
            return redirect(url_for('users'))

        user.is_active = not user.is_active
        db.session.commit()

        status = 'activated' if user.is_active else 'deactivated'
        flash(f'User {user.username} {status} successfully', 'success')

    except Exception as e:
        logger.error(f"Error toggling user status: {str(e)}")
        flash('Error updating user status', 'error')

    return redirect(url_for('users'))

# Initialize database
def init_db():
    """Initialize database tables"""
    with app.app_context():
        db.create_all()

        # Create default admin user if no users exist
        if User.query.count() == 0:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                role='admin',
                can_send_sms=True,
                can_view_inbox=True,
                can_view_outbox=True,
                can_export=True,
                can_manage_users=True,
                can_view_reports=True,
                can_manage_settings=True,
                can_manage_ports=True
            )
            admin_user.set_password('admin123')  # Change this in production!

            db.session.add(admin_user)
            db.session.commit()

            logger.info("Created default admin user: admin/admin123")

        # Create default SMS ports if none exist
        if SMSPort.query.count() == 0:
            default_ports = ['1', '2', '3', '4']
            for port_num in default_ports:
                port = SMSPort(
                    port_number=port_num,
                    status='unknown',
                    network_name='Unknown',
                    signal_quality='Unknown'
                )
                db.session.add(port)

            db.session.commit()
            logger.info("Created default SMS ports: 1, 2, 3, 4")

        logger.info("Database initialized")

# Start SMS receiver in background thread
def start_sms_receiver():
    """Start SMS receiver in background thread"""
    def receiver_thread():
        try:
            sms_receiver.start_listening()
        except Exception as e:
            logger.error(f"SMS receiver thread error: {str(e)}")

    thread = threading.Thread(target=receiver_thread, daemon=True)
    thread.start()
    logger.info("SMS receiver thread started")

if __name__ == '__main__':
    # Log current configuration
    logger.info("=== SMS Management System Starting ===")
    logger.info(f"SMS API IP: {SMS_API_CONFIG['ip']}")
    logger.info(f"SMS API Account: {SMS_API_CONFIG['account']}")
    logger.info(f"SMS API Port: {SMS_API_CONFIG['port']}")
    logger.info(f"TG SMS IP: {TG_SMS_CONFIG['ip']}")
    logger.info(f"TG SMS Port: {TG_SMS_CONFIG['port']}")
    logger.info(f"TG SMS Username: {TG_SMS_CONFIG['username']}")
    logger.info("=====================================")

    # Initialize database
    init_db()

    # Test TG SMS connection before starting receiver
    logger.info("Testing TG SMS connection...")
    test_receiver = SMSReceiver()
    if test_receiver.connect():
        logger.info("✅ TG SMS connection test successful")
        test_receiver.disconnect()

        # Start SMS receiver
        start_sms_receiver()
    else:
        logger.error("❌ TG SMS connection test failed")
        logger.error("SMS receiving will not work. Check:")
        logger.error("1. TG SMS service is running on Yeastar")
        logger.error("2. Port 5038 is accessible")
        logger.error("3. Username/password are correct")
        logger.error("4. Run: python test_receiving_only.py")

    # Run Flask app
    app.run(
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 5000)),
        debug=os.environ.get('DEBUG', 'False').lower() == 'true'
    )
