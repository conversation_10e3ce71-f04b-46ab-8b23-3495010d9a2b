import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  MessageSquare, 
  Send, 
  Inbox, 
  Clock, 
  AlertCircle, 
  TrendingUp,
  Edit3,
  RefreshCw,
  Activity
} from 'lucide-react';
import { smsApi } from '../services/api';

const Dashboard = ({ stats, loading }) => {
  const [recentMessages, setRecentMessages] = useState([]);
  const [ports, setPorts] = useState([]);
  const [loadingMessages, setLoadingMessages] = useState(true);
  const [loadingPorts, setLoadingPorts] = useState(true);

  useEffect(() => {
    loadRecentMessages();
    loadPorts();
  }, []);

  const loadRecentMessages = async () => {
    try {
      setLoadingMessages(true);
      const [inboxResponse, outboxResponse] = await Promise.all([
        smsApi.getInbox({ page: 1, per_page: 5 }),
        smsApi.getOutbox({ page: 1, per_page: 5 })
      ]);
      
      // Combine and sort by date
      const allMessages = [
        ...inboxResponse.data.messages,
        ...outboxResponse.data.messages
      ].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
      
      setRecentMessages(allMessages.slice(0, 10));
    } catch (error) {
      console.error('Error loading recent messages:', error);
    } finally {
      setLoadingMessages(false);
    }
  };

  const loadPorts = async () => {
    try {
      setLoadingPorts(true);
      const response = await smsApi.getPorts();
      setPorts(response.data);
    } catch (error) {
      console.error('Error loading ports:', error);
    } finally {
      setLoadingPorts(false);
    }
  };

  const StatCard = ({ icon: Icon, title, value, color, link, description }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className={`text-3xl font-bold ${color}`}>
            {loading ? '...' : value}
          </p>
          {description && (
            <p className="text-sm text-gray-500 mt-1">{description}</p>
          )}
        </div>
        <div className={`p-3 rounded-full ${color.replace('text-', 'bg-').replace('-600', '-100')}`}>
          <Icon className={`h-6 w-6 ${color}`} />
        </div>
      </div>
      {link && (
        <div className="mt-4">
          <Link 
            to={link}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            View all →
          </Link>
        </div>
      )}
    </div>
  );

  const formatDateTime = (dateString) => {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  const getStatusBadge = (status) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      sent: 'bg-green-100 text-green-800',
      delivered: 'bg-blue-100 text-blue-800',
      failed: 'bg-red-100 text-red-800',
      received: 'bg-purple-100 text-purple-800',
    };
    
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">SMS Dashboard</h1>
          <p className="text-gray-600 mt-1">Monitor and manage your SMS communications</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => {
              loadRecentMessages();
              loadPorts();
            }}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Refresh</span>
          </button>
          <Link
            to="/compose"
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Edit3 className="h-4 w-4" />
            <span>Compose SMS</span>
          </Link>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          icon={MessageSquare}
          title="Total Messages"
          value={stats.total_messages}
          color="text-blue-600"
          description="All SMS messages"
        />
        <StatCard
          icon={Inbox}
          title="Received"
          value={stats.total_received}
          color="text-green-600"
          link="/inbox"
          description="Incoming messages"
        />
        <StatCard
          icon={Send}
          title="Sent"
          value={stats.total_sent}
          color="text-purple-600"
          link="/outbox"
          description="Outgoing messages"
        />
        <StatCard
          icon={Clock}
          title="Pending"
          value={stats.pending_messages}
          color="text-yellow-600"
          description="Awaiting delivery"
        />
      </div>

      {/* Failed Messages Alert */}
      {stats.failed_messages > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <AlertCircle className="h-5 w-5 text-red-600" />
            <div>
              <h3 className="text-sm font-medium text-red-800">
                {stats.failed_messages} Failed Message{stats.failed_messages > 1 ? 's' : ''}
              </h3>
              <p className="text-sm text-red-600 mt-1">
                Some messages failed to send. Please check your outbox for details.
              </p>
            </div>
            <Link
              to="/outbox"
              className="ml-auto px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
            >
              View
            </Link>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Messages */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Recent Messages</h2>
              <TrendingUp className="h-5 w-5 text-gray-400" />
            </div>
          </div>
          <div className="p-6">
            {loadingMessages ? (
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : recentMessages.length > 0 ? (
              <div className="space-y-4">
                {recentMessages.map((message) => (
                  <div key={message.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className={`p-2 rounded-full ${
                      message.direction === 'inbound' ? 'bg-green-100' : 'bg-blue-100'
                    }`}>
                      {message.direction === 'inbound' ? (
                        <Inbox className={`h-4 w-4 ${
                          message.direction === 'inbound' ? 'text-green-600' : 'text-blue-600'
                        }`} />
                      ) : (
                        <Send className={`h-4 w-4 ${
                          message.direction === 'inbound' ? 'text-green-600' : 'text-blue-600'
                        }`} />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900">
                          {message.phone_number}
                        </p>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusBadge(message.status)}`}>
                          {message.status}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 truncate mt-1">
                        {message.content}
                      </p>
                      <p className="text-xs text-gray-400 mt-1">
                        {formatDateTime(message.created_at)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500">No messages yet</p>
                <Link
                  to="/compose"
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium mt-2 inline-block"
                >
                  Send your first message
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* SMS Ports Status */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">SMS Ports</h2>
              <Activity className="h-5 w-5 text-gray-400" />
            </div>
          </div>
          <div className="p-6">
            {loadingPorts ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  </div>
                ))}
              </div>
            ) : ports.length > 0 ? (
              <div className="space-y-4">
                {ports.map((port) => (
                  <div key={port.id} className="flex items-center justify-between p-3 rounded-lg border border-gray-200">
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        Port {port.port_number}
                      </p>
                      <p className="text-xs text-gray-500">
                        {port.network_name || 'Unknown Network'}
                      </p>
                      {port.signal_quality && (
                        <p className="text-xs text-gray-400">
                          Signal: {port.signal_quality}
                        </p>
                      )}
                    </div>
                    <div className="text-right">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        port.status?.includes('Up') 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {port.status?.includes('Up') ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Activity className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500">No ports configured</p>
                <Link
                  to="/settings"
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium mt-2 inline-block"
                >
                  Configure ports
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
