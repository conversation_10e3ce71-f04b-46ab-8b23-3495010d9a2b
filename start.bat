@echo off
echo Starting SMS Management System for MyGoautodial...
echo.

echo Installing dependencies...
call npm install
if errorlevel 1 (
    echo Failed to install root dependencies
    pause
    exit /b 1
)

echo Installing frontend dependencies...
cd frontend
call npm install
if errorlevel 1 (
    echo Failed to install frontend dependencies
    pause
    exit /b 1
)
cd ..

echo Installing backend dependencies...
cd backend
pip install -r ../requirements.txt
if errorlevel 1 (
    echo Failed to install backend dependencies
    pause
    exit /b 1
)
cd ..

echo.
echo Starting the application...
echo Backend will run on http://localhost:5000
echo Frontend will run on http://localhost:3000
echo.

start "SMS Backend" cmd /k "cd backend && python app.py"
timeout /t 3 /nobreak > nul
start "SMS Frontend" cmd /k "cd frontend && npm run dev"

echo.
echo SMS Management System is starting...
echo Check the opened terminal windows for status.
echo.
pause
