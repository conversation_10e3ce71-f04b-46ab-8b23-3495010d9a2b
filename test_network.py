#!/usr/bin/env python3
"""
Network connectivity test for Yeastar SMS system
Tests basic network connectivity before trying SMS protocols
"""

import socket
import subprocess
import sys

# Configuration
YEASTAR_IP = '*************'
YEASTAR_PORT = 5038

def test_ping():
    """Test if Yeastar IP is reachable via ping"""
    print(f"🏓 Testing ping to {YEASTAR_IP}...")
    
    try:
        # Use ping command (works on both Windows and Linux)
        if sys.platform.startswith('win'):
            result = subprocess.run(['ping', '-n', '4', YEASTAR_IP], 
                                  capture_output=True, text=True, timeout=10)
        else:
            result = subprocess.run(['ping', '-c', '4', YEASTAR_IP], 
                                  capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✅ Ping successful - {YEASTAR_IP} is reachable")
            return True
        else:
            print(f"❌ Ping failed - {YEASTAR_IP} is not reachable")
            print("Output:", result.stdout)
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ Ping timeout - {YEASTAR_IP} is not responding")
        return False
    except Exception as e:
        print(f"❌ Ping error: {e}")
        return False

def test_port_connectivity():
    """Test if port 5038 is open on Yeastar"""
    print(f"🔌 Testing port connectivity to {YEASTAR_IP}:{YEASTAR_PORT}...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        
        result = sock.connect_ex((YEASTAR_IP, YEASTAR_PORT))
        sock.close()
        
        if result == 0:
            print(f"✅ Port {YEASTAR_PORT} is open and accepting connections")
            return True
        else:
            print(f"❌ Port {YEASTAR_PORT} is closed or not accepting connections")
            return False
            
    except Exception as e:
        print(f"❌ Port test error: {e}")
        return False

def test_raw_connection():
    """Test raw TCP connection and see what we get"""
    print(f"🔍 Testing raw TCP connection to {YEASTAR_IP}:{YEASTAR_PORT}...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        print("Connecting...")
        sock.connect((YEASTAR_IP, YEASTAR_PORT))
        print("✅ Connected! Waiting for any initial data...")
        
        # Wait for any initial response
        sock.settimeout(3)
        try:
            initial_data = sock.recv(1024)
            if initial_data:
                print(f"📨 Received initial data: {initial_data}")
            else:
                print("📭 No initial data received")
        except socket.timeout:
            print("📭 No initial data received (timeout)")
        
        # Try sending a simple command
        print("Sending test command...")
        test_cmd = "Action: Login\r\nUsername: test\r\nSecret: test\r\n\r\n"
        sock.send(test_cmd.encode())
        
        # Wait for response
        sock.settimeout(5)
        try:
            response = sock.recv(1024)
            print(f"📨 Response received: {response}")
            return True
        except socket.timeout:
            print("📭 No response received (timeout)")
            return False
        
    except socket.timeout:
        print("❌ Connection timeout")
        return False
    except ConnectionRefusedError:
        print("❌ Connection refused")
        return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False
    finally:
        try:
            sock.close()
        except:
            pass

def check_firewall():
    """Check for common firewall issues"""
    print("🛡️ Checking for potential firewall issues...")
    
    # Check if Windows Firewall might be blocking
    if sys.platform.startswith('win'):
        try:
            result = subprocess.run(['netsh', 'advfirewall', 'show', 'allprofiles', 'state'], 
                                  capture_output=True, text=True, timeout=5)
            if 'ON' in result.stdout:
                print("⚠️  Windows Firewall is enabled - this might block connections")
                print("   Consider temporarily disabling it for testing")
            else:
                print("✅ Windows Firewall appears to be disabled")
        except:
            print("❓ Could not check Windows Firewall status")
    
    print("💡 Firewall troubleshooting tips:")
    print("   - Temporarily disable Windows Firewall")
    print("   - Check Yeastar firewall settings")
    print("   - Ensure port 5038 is allowed")

def main():
    """Run all network tests"""
    print("Yeastar Network Connectivity Test")
    print("=" * 40)
    print(f"Target: {YEASTAR_IP}:{YEASTAR_PORT}")
    print()
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Ping
    if test_ping():
        tests_passed += 1
    print()
    
    # Test 2: Port connectivity
    if test_port_connectivity():
        tests_passed += 1
    print()
    
    # Test 3: Raw connection
    if test_raw_connection():
        tests_passed += 1
    print()
    
    # Firewall check
    check_firewall()
    print()
    
    # Summary
    print("=" * 40)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("✅ All network tests passed!")
        print("   Network connectivity is working")
        print("   You can now test SMS protocols")
    elif tests_passed >= 1:
        print("⚠️  Some tests passed, some failed")
        print("   Basic connectivity exists but there may be issues")
    else:
        print("❌ All tests failed")
        print("   Check network configuration and Yeastar settings")
    
    print("\nNext steps:")
    if tests_passed >= 2:
        print("1. Run: python test_yeastar_sms.py")
        print("2. Check Yeastar TG SMS service configuration")
        print("3. Verify username/password credentials")
    else:
        print("1. Check Yeastar IP address (currently: {})".format(YEASTAR_IP))
        print("2. Verify Yeastar is powered on and network connected")
        print("3. Check firewall settings")
        print("4. Verify TG SMS service is running on Yeastar")

if __name__ == "__main__":
    main()
