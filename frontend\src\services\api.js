import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`);
    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('Response error:', error);
    
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.error || error.response.statusText;
      throw new Error(`Server Error (${error.response.status}): ${message}`);
    } else if (error.request) {
      // Request was made but no response received
      throw new Error('Network Error: Unable to connect to server');
    } else {
      // Something else happened
      throw new Error(`Request Error: ${error.message}`);
    }
  }
);

// SMS API endpoints
export const smsApi = {
  // Send SMS
  sendSMS: (data) => api.post('/sms/send', data),
  
  // Get inbox messages
  getInbox: (params = {}) => api.get('/sms/inbox', { params }),
  
  // Get outbox messages
  getOutbox: (params = {}) => api.get('/sms/outbox', { params }),
  
  // Get specific message
  getMessage: (messageId) => api.get(`/sms/message/${messageId}`),
  
  // Get SMS ports
  getPorts: () => api.get('/sms/ports'),
  
  // Get statistics
  getStats: () => api.get('/sms/stats'),
};

// Utility functions
export const formatPhoneNumber = (phoneNumber) => {
  // Remove all non-digit characters except +
  const cleaned = phoneNumber.replace(/[^\d+]/g, '');
  
  // Add + if it's missing and starts with a country code
  if (!cleaned.startsWith('+') && cleaned.length > 10) {
    return `+${cleaned}`;
  }
  
  return cleaned;
};

export const validatePhoneNumber = (phoneNumber) => {
  const cleaned = formatPhoneNumber(phoneNumber);
  // Basic validation for international format
  return /^\+?[1-9]\d{7,14}$/.test(cleaned);
};

export const formatDateTime = (dateString) => {
  try {
    const date = new Date(dateString);
    return date.toLocaleString();
  } catch (error) {
    return dateString;
  }
};

export const formatRelativeTime = (dateString) => {
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    }
  } catch (error) {
    return formatDateTime(dateString);
  }
};

export const truncateMessage = (message, maxLength = 100) => {
  if (message.length <= maxLength) {
    return message;
  }
  return message.substring(0, maxLength) + '...';
};

export const getStatusColor = (status) => {
  const colors = {
    pending: 'text-yellow-600 bg-yellow-100',
    sent: 'text-green-600 bg-green-100',
    delivered: 'text-blue-600 bg-blue-100',
    failed: 'text-red-600 bg-red-100',
    received: 'text-purple-600 bg-purple-100',
  };
  
  return colors[status] || 'text-gray-600 bg-gray-100';
};

export const getStatusIcon = (status) => {
  const icons = {
    pending: '⏳',
    sent: '✅',
    delivered: '📨',
    failed: '❌',
    received: '📥',
  };
  
  return icons[status] || '❓';
};

export default api;
