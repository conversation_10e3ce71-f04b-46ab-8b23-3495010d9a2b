{% extends "base.html" %}

{% block title %}SMS Ports - SMS Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-hdd-network"></i> SMS Port Management
            </h1>
            <div class="btn-group">
                <a href="{{ url_for('settings') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Settings
                </a>
                <button class="btn btn-primary" onclick="refreshPorts()">
                    <i class="bi bi-arrow-clockwise"></i> Refresh Status
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Port Status Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="bi bi-hdd-network fs-1"></i>
                <h4>{{ ports|length }}</h4>
                <small>Total Ports</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="bi bi-check-circle fs-1"></i>
                <h4>{{ ports|selectattr('status', 'equalto', 'active')|list|length }}</h4>
                <small>Active Ports</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body text-center">
                <i class="bi bi-exclamation-triangle fs-1"></i>
                <h4>{{ ports|selectattr('status', 'equalto', 'unknown')|list|length }}</h4>
                <small>Unknown Status</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="bi bi-people fs-1"></i>
                <h4>{{ ports|selectattr('status', 'equalto', 'assigned')|list|length }}</h4>
                <small>Assigned</small>
            </div>
        </div>
    </div>
</div>

<!-- Ports Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list"></i> SMS Ports Configuration
                </h5>
            </div>
            <div class="card-body">
                {% if ports %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Port Number</th>
                                    <th>Status</th>
                                    <th>Network</th>
                                    <th>Signal Quality</th>
                                    <th>SIM IMSI</th>
                                    <th>Last Updated</th>
                                    <th>Assigned Users</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for port in ports %}
                                <tr>
                                    <td>
                                        <strong class="text-primary">Port {{ port.port_number }}</strong>
                                    </td>
                                    <td>
                                        {% if port.status == 'active' %}
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle"></i> Active
                                            </span>
                                        {% elif port.status == 'inactive' %}
                                            <span class="badge bg-danger">
                                                <i class="bi bi-x-circle"></i> Inactive
                                            </span>
                                        {% else %}
                                            <span class="badge bg-warning">
                                                <i class="bi bi-question-circle"></i> {{ port.status|title or 'Unknown' }}
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>{{ port.network_name or 'Unknown' }}</td>
                                    <td>
                                        {% if port.signal_quality %}
                                            <div class="d-flex align-items-center">
                                                <div class="progress me-2" style="width: 60px; height: 8px;">
                                                    <div class="progress-bar bg-success" style="width: {{ port.signal_quality }}%"></div>
                                                </div>
                                                <small>{{ port.signal_quality }}%</small>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">Unknown</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="font-monospace">{{ port.sim_imsi or 'Not Available' }}</small>
                                    </td>
                                    <td>
                                        {% if port.last_updated %}
                                            <small>{{ port.last_updated.strftime('%Y-%m-%d %H:%M') }}</small>
                                        {% else %}
                                            <span class="text-muted">Never</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% set assigned_users = [] %}
                                        {% for user in users if port.port_number in user.get_assigned_ports() %}
                                            {% set _ = assigned_users.append(user.username) %}
                                        {% endfor %}
                                        
                                        {% if assigned_users %}
                                            {% for username in assigned_users %}
                                                <span class="badge bg-info me-1">{{ username }}</span>
                                            {% endfor %}
                                        {% else %}
                                            <span class="text-muted">Unassigned</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-info" 
                                                    onclick="testPort('{{ port.port_number }}')"
                                                    title="Test Port">
                                                <i class="bi bi-wifi"></i>
                                            </button>
                                            <button class="btn btn-outline-primary" 
                                                    onclick="configurePort('{{ port.port_number }}')"
                                                    title="Configure">
                                                <i class="bi bi-gear"></i>
                                            </button>
                                            <button class="btn btn-outline-success" 
                                                    onclick="refreshPortStatus('{{ port.port_number }}')"
                                                    title="Refresh Status">
                                                <i class="bi bi-arrow-clockwise"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-hdd-network text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">No SMS Ports Configured</h4>
                        <p class="text-muted">SMS ports will be automatically detected when the system connects to your SMS gateway.</p>
                        <button class="btn btn-primary" onclick="refreshPorts()">
                            <i class="bi bi-search"></i> Scan for Ports
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Port Information -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle"></i> Port Management Information
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Port Status Indicators:</h6>
                        <ul class="list-unstyled">
                            <li><span class="badge bg-success me-2">Active</span> Port is online and ready to send/receive SMS</li>
                            <li><span class="badge bg-danger me-2">Inactive</span> Port is offline or has issues</li>
                            <li><span class="badge bg-warning me-2">Unknown</span> Port status could not be determined</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Port Management:</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-wifi text-info me-2"></i> Test port connectivity and SMS capability</li>
                            <li><i class="bi bi-gear text-primary me-2"></i> Configure port settings and parameters</li>
                            <li><i class="bi bi-arrow-clockwise text-success me-2"></i> Refresh port status and information</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-info mt-3">
                    <i class="bi bi-lightbulb me-2"></i>
                    <strong>Tip:</strong> Assign specific ports to users in the User Management section to control which ports each user can access for sending and receiving SMS messages.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Refresh all ports status
function refreshPorts() {
    // Show loading state
    const refreshBtn = document.querySelector('button[onclick="refreshPorts()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Refreshing...';
    refreshBtn.disabled = true;
    
    // Simulate refresh (in real implementation, this would call the backend)
    setTimeout(() => {
        refreshBtn.innerHTML = originalText;
        refreshBtn.disabled = false;
        
        // Show success message
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show';
        alert.innerHTML = `
            <i class="bi bi-check-circle me-2"></i>
            Port status refreshed successfully!
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.querySelector('.container-fluid').insertBefore(alert, document.querySelector('.row'));
        
        // Auto-dismiss after 3 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
        
        // Reload page to show updated data
        location.reload();
    }, 2000);
}

// Test specific port
function testPort(portNumber) {
    alert(`Testing port ${portNumber}...\n\nThis would send a test SMS and check connectivity.\n\n(Feature would be implemented in production)`);
}

// Configure specific port
function configurePort(portNumber) {
    alert(`Configuring port ${portNumber}...\n\nThis would open port configuration settings.\n\n(Feature would be implemented in production)`);
}

// Refresh specific port status
function refreshPortStatus(portNumber) {
    alert(`Refreshing status for port ${portNumber}...\n\nThis would update the port's status information.\n\n(Feature would be implemented in production)`);
}
</script>
{% endblock %}
