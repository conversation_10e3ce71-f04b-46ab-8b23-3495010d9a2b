<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}SMS Manager - MyGoautodial{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="bi bi-chat-dots"></i> SMS Manager
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'index' %}active{% endif %}" href="{{ url_for('index') }}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'inbox' %}active{% endif %}" href="{{ url_for('inbox') }}">
                            <i class="bi bi-inbox"></i> Inbox
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'outbox' %}active{% endif %}" href="{{ url_for('outbox') }}">
                            <i class="bi bi-send"></i> Outbox
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'compose' %}active{% endif %}" href="{{ url_for('compose') }}">
                            <i class="bi bi-pencil-square"></i> Compose
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'settings' %}active{% endif %}" href="{{ url_for('settings') }}">
                            <i class="bi bi-gear"></i> Settings
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                        {% if current_user.has_permission('export') %}
                            <li class="nav-item">
                                <a class="nav-link {% if request.endpoint == 'export_page' %}active{% endif %}" href="{{ url_for('export_page') }}">
                                    <i class="bi bi-download"></i> Export
                                </a>
                            </li>
                        {% endif %}
                        {% if current_user.has_permission('view_reports') %}
                            <li class="nav-item">
                                <a class="nav-link {% if request.endpoint == 'reports' %}active{% endif %}" href="{{ url_for('reports') }}">
                                    <i class="bi bi-graph-up"></i> Reports
                                </a>
                            </li>
                        {% endif %}
                        {% if current_user.has_permission('manage_users') %}
                            <li class="nav-item">
                                <a class="nav-link {% if request.endpoint == 'users' %}active{% endif %}" href="{{ url_for('users') }}">
                                    <i class="bi bi-people"></i> Users
                                </a>
                            </li>
                        {% endif %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle"></i> {{ current_user.username }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><h6 class="dropdown-header">{{ current_user.email }}</h6></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><span class="dropdown-item-text"><small>Role: {{ current_user.role.title() }}</small></span></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('debug') }}">
                                    <i class="bi bi-bug me-2"></i>Debug
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="location.reload()">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                    <i class="bi bi-box-arrow-right me-2"></i>Logout
                                </a></li>
                            </ul>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container-fluid mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {% if category == 'success' %}
                            <i class="bi bi-check-circle"></i>
                        {% elif category == 'error' %}
                            <i class="bi bi-exclamation-triangle"></i>
                        {% else %}
                            <i class="bi bi-info-circle"></i>
                        {% endif %}
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main class="container-fluid py-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <small class="text-muted">
                SMS Management System for MyGoautodial &copy; 2024
            </small>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
