{% extends "base.html" %}

{% block title %}Outbox - SMS Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-send"></i> Outbox
                {% if messages %}
                    <small class="text-muted">({{ messages.total }} messages)</small>
                {% endif %}
            </h1>
            <div>
                <button onclick="location.reload()" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-clockwise"></i> Refresh
                </button>
                <a href="{{ url_for('compose') }}" class="btn btn-primary">
                    <i class="bi bi-pencil-square"></i> Compose
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Status Filter Tabs -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <ul class="nav nav-pills">
                    <li class="nav-item">
                        <a class="nav-link {% if not request.args.get('status') %}active{% endif %}"
                           href="{{ url_for('outbox') }}">
                            All Messages
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.args.get('status') == 'pending' %}active{% endif %}"
                           href="{{ url_for('outbox', status='pending') }}">
                            <i class="bi bi-clock"></i> Pending
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.args.get('status') == 'sent' %}active{% endif %}"
                           href="{{ url_for('outbox', status='sent') }}">
                            <i class="bi bi-check-circle"></i> Sent
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.args.get('status') == 'failed' %}active{% endif %}"
                           href="{{ url_for('outbox', status='failed') }}">
                            <i class="bi bi-x-circle"></i> Failed
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Search -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <input type="hidden" name="status" value="{{ request.args.get('status', '') }}">
                    <div class="col-md-8">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-search"></i>
                            </span>
                            <input type="text"
                                   class="form-control"
                                   name="search"
                                   placeholder="Search messages or phone numbers..."
                                   value="{{ request.args.get('search', '') }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-outline-secondary w-100">
                            <i class="bi bi-search"></i> Search
                        </button>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('outbox') }}" class="btn btn-outline-danger w-100">
                            <i class="bi bi-x"></i> Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Messages List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list"></i> Sent Messages
                </h5>
            </div>
            <div class="card-body p-0">
                {% if messages and messages.items %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th width="5%">
                                        <input type="checkbox" class="form-check-input" id="selectAll">
                                    </th>
                                    <th width="15%">To</th>
                                    <th width="40%">Message</th>
                                    <th width="10%">Status</th>
                                    <th width="10%">Port</th>
                                    <th width="15%">Sent</th>
                                    <th width="5%">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for message in messages.items %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input message-checkbox" value="{{ message.id }}">
                                    </td>
                                    <td>
                                        <strong>{{ message.phone_number }}</strong>
                                    </td>
                                    <td>
                                        <div class="message-content">
                                            {{ message.content[:80] }}{% if message.content|length > 80 %}...{% endif %}
                                        </div>
                                        {% if message.reply_to_id %}
                                            <small class="text-muted">
                                                <i class="bi bi-reply"></i> Reply to {{ message.reply_to_id[:8] }}...
                                            </small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if message.status == 'sent' %}
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle"></i> Sent
                                            </span>
                                        {% elif message.status == 'pending' %}
                                            <span class="badge bg-warning">
                                                <i class="bi bi-clock"></i> Pending
                                            </span>
                                        {% elif message.status == 'failed' %}
                                            <span class="badge bg-danger">
                                                <i class="bi bi-x-circle"></i> Failed
                                            </span>
                                        {% elif message.status == 'delivered' %}
                                            <span class="badge bg-primary">
                                                <i class="bi bi-check-all"></i> Delivered
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ message.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if message.gsm_port %}
                                            <span class="badge bg-secondary">Port {{ message.gsm_port }}</span>
                                        {% else %}
                                            <span class="text-muted">Auto</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ message.created_at.strftime('%Y-%m-%d') }}<br>
                                            {{ message.created_at.strftime('%H:%M:%S') }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('conversation_view', phone_number=message.phone_number) }}"
                                               class="btn btn-outline-success btn-sm"
                                               title="View Conversation">
                                                <i class="bi bi-chat-dots"></i>
                                            </a>
                                            {% if message.status == 'failed' %}
                                                <button class="btn btn-outline-warning btn-sm"
                                                        onclick="resendMessage('{{ message.id }}')"
                                                        title="Resend">
                                                    <i class="bi bi-arrow-clockwise"></i>
                                                </button>
                                            {% endif %}
                                            <button class="btn btn-outline-info btn-sm"
                                                    onclick="showMessageDetails('{{ message.id }}')"
                                                    title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if messages.pages > 1 %}
                    <div class="card-footer">
                        <nav aria-label="Message pagination">
                            <ul class="pagination justify-content-center mb-0">
                                {% if messages.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('outbox', page=messages.prev_num, status=request.args.get('status', ''), search=request.args.get('search', '')) }}">
                                            <i class="bi bi-chevron-left"></i> Previous
                                        </a>
                                    </li>
                                {% endif %}

                                {% for page_num in messages.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != messages.page %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('outbox', page=page_num, status=request.args.get('status', ''), search=request.args.get('search', '')) }}">{{ page_num }}</a>
                                            </li>
                                        {% else %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ page_num }}</span>
                                            </li>
                                        {% endif %}
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if messages.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('outbox', page=messages.next_num, status=request.args.get('status', ''), search=request.args.get('search', '')) }}">
                                            Next <i class="bi bi-chevron-right"></i>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>

                        <div class="text-center mt-2">
                            <small class="text-muted">
                                Showing {{ ((messages.page - 1) * messages.per_page) + 1 }} to
                                {{ messages.page * messages.per_page if messages.page * messages.per_page < messages.total else messages.total }}
                                of {{ messages.total }} messages
                            </small>
                        </div>
                    </div>
                    {% endif %}

                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-send text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">No messages in outbox</h4>
                        <p class="text-muted">You haven't sent any SMS messages yet.</p>
                        <a href="{{ url_for('compose') }}" class="btn btn-primary">
                            <i class="bi bi-pencil-square"></i> Send your first message
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Bulk Actions -->
<div id="bulkActions" class="position-fixed bottom-0 start-50 translate-middle-x bg-primary text-white p-3 rounded-top shadow" style="display: none; z-index: 1000;">
    <div class="d-flex align-items-center gap-3">
        <span id="selectedCount">0</span> message(s) selected
        <button class="btn btn-light btn-sm" onclick="deleteSelected()">
            <i class="bi bi-trash"></i> Delete
        </button>
        <button class="btn btn-outline-light btn-sm" onclick="clearSelection()">
            <i class="bi bi-x"></i> Clear
        </button>
    </div>
</div>

<!-- Message Details Modal -->
<div class="modal fade" id="messageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Message Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="messageModalBody">
                <!-- Content loaded via JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Message selection handling
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const messageCheckboxes = document.querySelectorAll('.message-checkbox');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');

    function updateBulkActions() {
        const selected = document.querySelectorAll('.message-checkbox:checked');
        if (selected.length > 0) {
            selectedCount.textContent = selected.length;
            bulkActions.style.display = 'block';
        } else {
            bulkActions.style.display = 'none';
        }
    }

    selectAllCheckbox?.addEventListener('change', function() {
        messageCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });

    messageCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });

    window.clearSelection = function() {
        messageCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        selectAllCheckbox.checked = false;
        updateBulkActions();
    };

    window.deleteSelected = function() {
        const selected = document.querySelectorAll('.message-checkbox:checked');
        if (selected.length > 0 && confirm(`Delete ${selected.length} message(s)?`)) {
            // Implementation would go here
            alert('Delete functionality would be implemented here');
        }
    };
});

// Resend message
function resendMessage(messageId) {
    if (confirm('Resend this message?')) {
        // Implementation would go here
        alert('Resend functionality would be implemented here');
    }
}

// Show message details
function showMessageDetails(messageId) {
    const modal = new bootstrap.Modal(document.getElementById('messageModal'));
    document.getElementById('messageModalBody').innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `;
    modal.show();

    // Simulate loading message details
    setTimeout(() => {
        document.getElementById('messageModalBody').innerHTML = `
            <p><strong>Message ID:</strong> ${messageId}</p>
            <p>Message details would be loaded here via AJAX call to the backend.</p>
        `;
    }, 1000);
}

// Auto-refresh every 30 seconds
setInterval(function() {
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 30000);
</script>
{% endblock %}
