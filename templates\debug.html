{% extends "base.html" %}

{% block title %}SMS Debug - SMS Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-bug"></i> SMS Debug & Testing
        </h1>
    </div>
</div>

<!-- SMS Receiver Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-activity"></i> SMS Receiver Status
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-primary w-100 mb-3" onclick="checkSMSStatus()">
                            <i class="bi bi-arrow-clockwise"></i> Check Status
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-warning w-100 mb-3" onclick="reconnectSMS()">
                            <i class="bi bi-wifi"></i> Reconnect to TG SMS
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-info w-100 mb-3" onclick="testTGConnection()">
                            <i class="bi bi-router"></i> Test TG Connection
                        </button>
                    </div>
                </div>
                <div id="smsStatus" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<!-- Configuration Check -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i> Configuration Check
                </h5>
            </div>
            <div class="card-body">
                <button class="btn btn-info w-100 mb-3" onclick="checkConfig()">
                    <i class="bi bi-list-check"></i> Check Current Configuration
                </button>
                <div id="configStatus" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<!-- Test SMS Receiving -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-envelope-check"></i> Test SMS Receiving & Database
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <p class="text-muted small">
                            Test SMS parsing and database saving
                        </p>
                        <button class="btn btn-success w-100 mb-2" onclick="testSMSReceive()">
                            <i class="bi bi-play-circle"></i> Test SMS Parsing
                        </button>
                    </div>
                    <div class="col-md-3">
                        <p class="text-muted small">
                            Force save test SMS to database
                        </p>
                        <button class="btn btn-warning w-100 mb-2" onclick="forceSaveTestSMS()">
                            <i class="bi bi-database-add"></i> Force Save Test SMS
                        </button>
                    </div>
                    <div class="col-md-3">
                        <p class="text-muted small">
                            Check database contents
                        </p>
                        <button class="btn btn-info w-100 mb-2" onclick="checkDatabase()">
                            <i class="bi bi-database"></i> Check Database
                        </button>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-12">
                        <p class="text-muted small">
                            Check ALL messages in database (including outbound and other directions)
                        </p>
                        <button class="btn btn-warning w-100 mb-2" onclick="checkAllMessages()">
                            <i class="bi bi-database-fill"></i> Check ALL Messages (Debug Missing SMS)
                        </button>
                    </div>
                    <div class="col-md-3">
                        <p class="text-muted small">
                            Test inbox query directly
                        </p>
                        <button class="btn btn-primary w-100 mb-2" onclick="testInboxQuery()">
                            <i class="bi bi-inbox"></i> Test Inbox Query
                        </button>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-6">
                        <p class="text-muted small">
                            Test inbox page rendering (bypasses auth)
                        </p>
                        <button class="btn btn-secondary w-100 mb-2" onclick="testInboxPage()">
                            <i class="bi bi-eye"></i> Test Inbox Page
                        </button>
                    </div>
                    <div class="col-md-6">
                        <p class="text-muted small">
                            Open actual inbox (requires login)
                        </p>
                        <a href="/inbox" class="btn btn-outline-primary w-100 mb-2" target="_blank">
                            <i class="bi bi-box-arrow-up-right"></i> Open Real Inbox
                        </a>
                    </div>
                </div>
                <div id="testResults" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<!-- Connection Troubleshooting -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-question-circle"></i> Troubleshooting Guide
                </h5>
            </div>
            <div class="card-body">
                <h6>Common Issues and Solutions:</h6>
                <div class="accordion" id="troubleshootingAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#issue1">
                                Connection Refused Error
                            </button>
                        </h2>
                        <div id="issue1" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                            <div class="accordion-body">
                                <ul>
                                    <li>Check if TG SMS server is running on <code>*************:5038</code></li>
                                    <li>Verify the IP address is correct</li>
                                    <li>Check firewall settings</li>
                                    <li>Test connection with: <code>telnet ************* 5038</code></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#issue2">
                                Authentication Failed
                            </button>
                        </h2>
                        <div id="issue2" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                            <div class="accordion-body">
                                <ul>
                                    <li>Check username and password in Settings</li>
                                    <li>Verify credentials with TG SMS server admin</li>
                                    <li>Check if account has SMS receiving permissions</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#issue3">
                                No Messages Received
                            </button>
                        </h2>
                        <div id="issue3" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                            <div class="accordion-body">
                                <ul>
                                    <li>Check if SMS receiver is connected and running</li>
                                    <li>Verify TG SMS server is configured to send events</li>
                                    <li>Check SMS event format matches expected format</li>
                                    <li>Test with the "Test SMS Receive" button above</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Real-time Logs -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-terminal"></i> Debug Information
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">
                    Check the server console/terminal for real-time SMS receiving logs.
                    Look for messages like:
                </p>
                <div class="bg-dark text-light p-3 rounded">
                    <code>
                        📱 Started listening for incoming SMS messages...<br>
                        📨 Received data from TG SMS server: Event: ReceivedSMS...<br>
                        ✅ Successfully parsed SMS: ID=123, From=+1234567890...
                    </code>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
async function checkSMSStatus() {
    const statusDiv = document.getElementById('smsStatus');
    statusDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Checking SMS status...';

    try {
        const response = await fetch('/debug/sms_status');
        const data = await response.json();

        let statusHtml = '<div class="row">';

        // Connection Status
        statusHtml += '<div class="col-md-6">';
        statusHtml += `<div class="alert ${data.receiver_connected ? 'alert-success' : 'alert-danger'}">`;
        statusHtml += `<strong>Connection:</strong> ${data.receiver_connected ? '✅ Connected' : '❌ Disconnected'}`;
        statusHtml += '</div></div>';

        // Running Status
        statusHtml += '<div class="col-md-6">';
        statusHtml += `<div class="alert ${data.receiver_running ? 'alert-success' : 'alert-warning'}">`;
        statusHtml += `<strong>Receiver:</strong> ${data.receiver_running ? '🏃 Running' : '⏸️ Stopped'}`;
        statusHtml += '</div></div>';

        statusHtml += '</div>';

        // Configuration
        statusHtml += '<div class="mt-3">';
        statusHtml += '<h6>TG SMS Configuration:</h6>';
        statusHtml += '<ul>';
        statusHtml += `<li><strong>IP:</strong> ${data.tg_sms_config.ip}</li>`;
        statusHtml += `<li><strong>Port:</strong> ${data.tg_sms_config.port}</li>`;
        statusHtml += `<li><strong>Username:</strong> ${data.tg_sms_config.username}</li>`;
        statusHtml += `<li><strong>Total Received Messages:</strong> ${data.total_received_messages}</li>`;
        statusHtml += '</ul>';
        statusHtml += '</div>';

        statusDiv.innerHTML = statusHtml;

    } catch (error) {
        statusDiv.innerHTML = `<div class="alert alert-danger">Error checking status: ${error.message}</div>`;
    }
}

async function reconnectSMS() {
    const statusDiv = document.getElementById('smsStatus');
    statusDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Reconnecting to TG SMS server...';

    try {
        const response = await fetch('/debug/reconnect_sms');
        const data = await response.json();

        if (data.success) {
            statusDiv.innerHTML = `<div class="alert alert-success">✅ ${data.message}</div>`;
        } else {
            statusDiv.innerHTML = `<div class="alert alert-danger">❌ ${data.message}<br>Error: ${data.error || 'Unknown error'}</div>`;
        }

        // Refresh status after reconnection attempt
        setTimeout(checkSMSStatus, 2000);

    } catch (error) {
        statusDiv.innerHTML = `<div class="alert alert-danger">Error reconnecting: ${error.message}</div>`;
    }
}

async function checkConfig() {
    const configDiv = document.getElementById('configStatus');
    configDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Checking configuration...';

    try {
        const response = await fetch('/debug/config');
        const data = await response.json();

        let configHtml = '<div class="row">';

        // SMS API Config
        configHtml += '<div class="col-md-6">';
        configHtml += '<h6>SMS API Configuration:</h6>';
        configHtml += '<ul>';
        configHtml += `<li><strong>IP:</strong> ${data.SMS_API_CONFIG.ip}</li>`;
        configHtml += `<li><strong>Account:</strong> ${data.SMS_API_CONFIG.account}</li>`;
        configHtml += `<li><strong>Port:</strong> ${data.SMS_API_CONFIG.port}</li>`;
        configHtml += '</ul>';
        configHtml += '</div>';

        // TG SMS Config
        configHtml += '<div class="col-md-6">';
        configHtml += '<h6>TG SMS Configuration:</h6>';
        configHtml += '<ul>';
        configHtml += `<li><strong>IP:</strong> ${data.TG_SMS_CONFIG.ip}</li>`;
        configHtml += `<li><strong>Port:</strong> ${data.TG_SMS_CONFIG.port}</li>`;
        configHtml += `<li><strong>Username:</strong> ${data.TG_SMS_CONFIG.username}</li>`;
        configHtml += '</ul>';
        configHtml += '</div>';

        configHtml += '</div>';

        configDiv.innerHTML = configHtml;

    } catch (error) {
        configDiv.innerHTML = `<div class="alert alert-danger">Error checking config: ${error.message}</div>`;
    }
}

async function testSMSReceive() {
    const resultsDiv = document.getElementById('testResults');
    resultsDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Testing SMS receive processing...';

    try {
        const response = await fetch('/debug/test_sms_receive', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const data = await response.json();

        if (data.success) {
            resultsDiv.innerHTML = `
                <div class="alert alert-success">
                    ✅ ${data.message}
                    <details class="mt-2">
                        <summary>Parsed Data:</summary>
                        <pre class="mt-2">${JSON.stringify(data.parsed_data, null, 2)}</pre>
                    </details>
                    <details class="mt-2">
                        <summary>Sample Data Used:</summary>
                        <pre class="mt-2">${data.sample_data}</pre>
                    </details>
                </div>
            `;
        } else {
            resultsDiv.innerHTML = `<div class="alert alert-danger">❌ Test failed: ${data.message || data.error}</div>`;
        }

    } catch (error) {
        resultsDiv.innerHTML = `<div class="alert alert-danger">Error testing SMS receive: ${error.message}</div>`;
    }
}

async function forceSaveTestSMS() {
    const resultsDiv = document.getElementById('testResults');
    resultsDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Force saving test SMS to database...';

    try {
        const response = await fetch('/debug/force_save_test_sms', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const data = await response.json();

        if (data.success) {
            resultsDiv.innerHTML = `
                <div class="alert alert-success">
                    ✅ ${data.message}
                    <details class="mt-2">
                        <summary>Saved Message Details:</summary>
                        <pre class="mt-2">${JSON.stringify(data.saved_message, null, 2)}</pre>
                    </details>
                    <p class="mt-2 mb-0">
                        <small class="text-muted">
                            Check the inbox to see if this message appears in the GUI.
                        </small>
                    </p>
                </div>
            `;
        } else {
            resultsDiv.innerHTML = `<div class="alert alert-danger">❌ Force save failed: ${data.message || data.error}</div>`;
        }

    } catch (error) {
        resultsDiv.innerHTML = `<div class="alert alert-danger">Error force saving SMS: ${error.message}</div>`;
    }
}

async function checkDatabase() {
    const resultsDiv = document.getElementById('testResults');
    resultsDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Checking database contents...';

    try {
        const response = await fetch('/debug/database_check');
        const data = await response.json();

        if (data.total_messages !== undefined) {
            let html = `
                <div class="alert alert-info">
                    📊 <strong>Database Status:</strong>
                    <ul class="mb-0 mt-2">
                        <li>Total Messages: ${data.total_messages}</li>
                        <li>Inbound (Received): ${data.inbound_count}</li>
                        <li>Outbound (Sent): ${data.outbound_count}</li>
                    </ul>
                </div>
            `;

            if (data.recent_messages && data.recent_messages.length > 0) {
                html += `
                    <div class="alert alert-success">
                        📨 <strong>Recent Messages:</strong>
                        <div class="table-responsive mt-2">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Direction</th>
                                        <th>Phone</th>
                                        <th>Content</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                `;

                data.recent_messages.forEach(msg => {
                    html += `
                        <tr>
                            <td>${msg.id}</td>
                            <td><span class="badge bg-${msg.direction === 'inbound' ? 'success' : 'primary'}">${msg.direction}</span></td>
                            <td>${msg.phone_number}</td>
                            <td>${msg.content}</td>
                            <td><span class="badge bg-secondary">${msg.status}</span></td>
                            <td>${msg.created_at ? new Date(msg.created_at).toLocaleString() : 'N/A'}</td>
                        </tr>
                    `;
                });

                html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;
            }

            if (data.all_inbound_messages && data.all_inbound_messages.length > 0) {
                html += `
                    <details class="mt-2">
                        <summary><strong>All Inbound Messages (${data.all_inbound_messages.length})</strong></summary>
                        <pre class="mt-2">${JSON.stringify(data.all_inbound_messages, null, 2)}</pre>
                    </details>
                `;
            }

            resultsDiv.innerHTML = html;
        } else {
            resultsDiv.innerHTML = `<div class="alert alert-danger">❌ Database check failed: ${data.error}</div>`;
        }

    } catch (error) {
        resultsDiv.innerHTML = `<div class="alert alert-danger">Error checking database: ${error.message}</div>`;
    }
}

async function testInboxQuery() {
    const resultsDiv = document.getElementById('testResults');
    resultsDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Testing inbox query directly...';

    try {
        const response = await fetch('/debug/test_inbox_query');
        const data = await response.json();

        if (data.success) {
            let html = `
                <div class="alert alert-success">
                    ✅ <strong>Inbox Query Test Results:</strong>
                    <ul class="mb-0 mt-2">
                        <li>Total inbound messages in DB: ${data.raw_inbound_count}</li>
                        <li>Total all messages in DB: ${data.all_messages_count}</li>
                        <li>Paginated query total: ${data.query_results.total}</li>
                        <li>Messages on page 1: ${data.query_results.messages.length}</li>
                    </ul>
                </div>
            `;

            if (data.query_results.messages.length > 0) {
                html += `
                    <div class="alert alert-info">
                        📨 <strong>Messages found by inbox query:</strong>
                        <div class="table-responsive mt-2">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Message ID</th>
                                        <th>Phone</th>
                                        <th>Content</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                `;

                data.query_results.messages.forEach(msg => {
                    html += `
                        <tr>
                            <td>${msg.id}</td>
                            <td>${msg.message_id}</td>
                            <td>${msg.phone_number}</td>
                            <td>${msg.content.substring(0, 30)}${msg.content.length > 30 ? '...' : ''}</td>
                            <td><span class="badge bg-secondary">${msg.status}</span></td>
                            <td>${msg.created_at ? new Date(msg.created_at).toLocaleString() : 'N/A'}</td>
                        </tr>
                    `;
                });

                html += `
                                </tbody>
                            </table>
                        </div>
                        <p class="mt-2 mb-0">
                            <strong>🔍 If messages appear here but not in the GUI inbox, there's an authentication or template issue.</strong>
                        </p>
                    </div>
                `;
            } else {
                html += `
                    <div class="alert alert-warning">
                        ⚠️ <strong>No messages found by inbox query</strong>
                        <p class="mb-0 mt-2">
                            This means either no SMS messages are being saved to the database,
                            or they're being saved with a different direction value.
                        </p>
                    </div>
                `;
            }

            resultsDiv.innerHTML = html;
        } else {
            resultsDiv.innerHTML = `<div class="alert alert-danger">❌ Inbox query test failed: ${data.error}</div>`;
        }

    } catch (error) {
        resultsDiv.innerHTML = `<div class="alert alert-danger">Error testing inbox query: ${error.message}</div>`;
    }
}

async function checkAllMessages() {
    const resultsDiv = document.getElementById('testResults');
    resultsDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Checking ALL messages in database...';

    try {
        const response = await fetch('/debug/check_all_messages');
        const data = await response.json();

        if (data.success) {
            let html = `
                <div class="alert alert-info">
                    📊 <strong>Complete Database Analysis:</strong>
                    <ul class="mb-0 mt-2">
                        <li><strong>Total Messages:</strong> ${data.summary.total_messages}</li>
                        <li><strong>Inbound (Received):</strong> ${data.summary.inbound_count}</li>
                        <li><strong>Outbound (Sent):</strong> ${data.summary.outbound_count}</li>
                        <li><strong>Other Direction:</strong> ${data.summary.other_direction_count}</li>
                    </ul>
                </div>
            `;

            if (data.summary.total_messages > data.summary.inbound_count) {
                html += `
                    <div class="alert alert-warning">
                        ⚠️ <strong>Found ${data.summary.total_messages - data.summary.inbound_count} messages that are NOT marked as 'inbound'!</strong>
                        <p class="mb-0 mt-2">
                            This might explain why you're only seeing ${data.summary.inbound_count} messages in the inbox.
                            Some SMS might be saved with wrong direction.
                        </p>
                    </div>
                `;
            }

            if (data.all_messages && data.all_messages.length > 0) {
                html += `
                    <div class="alert alert-success">
                        📨 <strong>All Messages in Database:</strong>
                        <div class="table-responsive mt-2">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Direction</th>
                                        <th>Phone</th>
                                        <th>Content</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Port</th>
                                    </tr>
                                </thead>
                                <tbody>
                `;

                data.all_messages.forEach(msg => {
                    const directionColor = msg.direction === 'inbound' ? 'success' :
                                         msg.direction === 'outbound' ? 'primary' : 'warning';
                    html += `
                        <tr>
                            <td>${msg.id}</td>
                            <td><span class="badge bg-${directionColor}">${msg.direction}</span></td>
                            <td>${msg.phone_number}</td>
                            <td>${msg.content}</td>
                            <td><span class="badge bg-secondary">${msg.status}</span></td>
                            <td>${msg.created_at ? new Date(msg.created_at).toLocaleString() : 'N/A'}</td>
                            <td>${msg.gsm_port || '-'}</td>
                        </tr>
                    `;
                });

                html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;
            }

            if (data.other_messages && data.other_messages.length > 0) {
                html += `
                    <div class="alert alert-danger">
                        🚨 <strong>Messages with WRONG Direction Found:</strong>
                        <p class="mt-2">These messages are not showing in inbox because they're not marked as 'inbound':</p>
                        <pre class="mt-2">${JSON.stringify(data.other_messages, null, 2)}</pre>
                    </div>
                `;
            }

            resultsDiv.innerHTML = html;
        } else {
            resultsDiv.innerHTML = `<div class="alert alert-danger">❌ Failed to check all messages: ${data.error}</div>`;
        }

    } catch (error) {
        resultsDiv.innerHTML = `<div class="alert alert-danger">Error checking all messages: ${error.message}</div>`;
    }
}

async function testTGConnection() {
    const statusDiv = document.getElementById('smsStatus');
    statusDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Testing TG SMS server connection...';

    try {
        const response = await fetch('/debug/test_tg_connection');
        const data = await response.json();

        if (data.success) {
            let html = `
                <div class="alert ${data.connection_test.can_connect ? 'alert-success' : 'alert-danger'}">
                    <h6><strong>TG SMS Server Connection Test:</strong></h6>
                    <ul class="mb-2">
                        <li><strong>Server:</strong> ${data.connection_test.host}:${data.connection_test.port}</li>
                        <li><strong>Status:</strong> ${data.connection_test.status}</li>
                    </ul>
                </div>
            `;

            html += `
                <div class="alert alert-info">
                    <h6><strong>Current Receiver Status:</strong></h6>
                    <ul class="mb-2">
                        <li><strong>Connected:</strong> ${data.receiver_status.connected ? '✅ Yes' : '❌ No'}</li>
                        <li><strong>Running:</strong> ${data.receiver_status.running ? '✅ Yes' : '❌ No'}</li>
                        <li><strong>Socket:</strong> ${data.receiver_status.socket_exists ? '✅ Exists' : '❌ None'}</li>
                    </ul>
                </div>
            `;

            html += `
                <div class="alert alert-warning">
                    <h6><strong>Recommendations:</strong></h6>
                    <ul class="mb-0">
            `;

            data.recommendations.forEach(rec => {
                html += `<li>${rec}</li>`;
            });

            html += `
                    </ul>
                </div>
            `;

            statusDiv.innerHTML = html;
        } else {
            statusDiv.innerHTML = `<div class="alert alert-danger">❌ Connection test failed: ${data.error}</div>`;
        }

    } catch (error) {
        statusDiv.innerHTML = `<div class="alert alert-danger">Error testing TG connection: ${error.message}</div>`;
    }
}

function testInboxPage() {
    // Open the test inbox page in a new window
    window.open('/debug/test_inbox_page', '_blank');
}

// Auto-check status on page load
document.addEventListener('DOMContentLoaded', function() {
    checkSMSStatus();
});
</script>
{% endblock %}
