{% extends "base.html" %}

{% block title %}SMS Debug - SMS Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-bug"></i> SMS Debug & Testing
        </h1>
    </div>
</div>

<!-- SMS Receiver Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-activity"></i> SMS Receiver Status
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-primary w-100 mb-3" onclick="checkSMSStatus()">
                            <i class="bi bi-arrow-clockwise"></i> Check Status
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-warning w-100 mb-3" onclick="reconnectSMS()">
                            <i class="bi bi-wifi"></i> Reconnect to TG SMS
                        </button>
                    </div>
                </div>
                <div id="smsStatus" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<!-- Configuration Check -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i> Configuration Check
                </h5>
            </div>
            <div class="card-body">
                <button class="btn btn-info w-100 mb-3" onclick="checkConfig()">
                    <i class="bi bi-list-check"></i> Check Current Configuration
                </button>
                <div id="configStatus" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<!-- Test SMS Receiving -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-envelope-check"></i> Test SMS Receiving
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">
                    This will simulate receiving an SMS message to test the parsing and database saving functionality.
                </p>
                <button class="btn btn-success w-100 mb-3" onclick="testSMSReceive()">
                    <i class="bi bi-play-circle"></i> Test SMS Receive Processing
                </button>
                <div id="testResults" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<!-- Connection Troubleshooting -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-question-circle"></i> Troubleshooting Guide
                </h5>
            </div>
            <div class="card-body">
                <h6>Common Issues and Solutions:</h6>
                <div class="accordion" id="troubleshootingAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#issue1">
                                Connection Refused Error
                            </button>
                        </h2>
                        <div id="issue1" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                            <div class="accordion-body">
                                <ul>
                                    <li>Check if TG SMS server is running on <code>*************:5038</code></li>
                                    <li>Verify the IP address is correct</li>
                                    <li>Check firewall settings</li>
                                    <li>Test connection with: <code>telnet ************* 5038</code></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#issue2">
                                Authentication Failed
                            </button>
                        </h2>
                        <div id="issue2" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                            <div class="accordion-body">
                                <ul>
                                    <li>Check username and password in Settings</li>
                                    <li>Verify credentials with TG SMS server admin</li>
                                    <li>Check if account has SMS receiving permissions</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#issue3">
                                No Messages Received
                            </button>
                        </h2>
                        <div id="issue3" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                            <div class="accordion-body">
                                <ul>
                                    <li>Check if SMS receiver is connected and running</li>
                                    <li>Verify TG SMS server is configured to send events</li>
                                    <li>Check SMS event format matches expected format</li>
                                    <li>Test with the "Test SMS Receive" button above</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Real-time Logs -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-terminal"></i> Debug Information
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">
                    Check the server console/terminal for real-time SMS receiving logs.
                    Look for messages like:
                </p>
                <div class="bg-dark text-light p-3 rounded">
                    <code>
                        📱 Started listening for incoming SMS messages...<br>
                        📨 Received data from TG SMS server: Event: ReceivedSMS...<br>
                        ✅ Successfully parsed SMS: ID=123, From=+1234567890...
                    </code>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
async function checkSMSStatus() {
    const statusDiv = document.getElementById('smsStatus');
    statusDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Checking SMS status...';
    
    try {
        const response = await fetch('/debug/sms_status');
        const data = await response.json();
        
        let statusHtml = '<div class="row">';
        
        // Connection Status
        statusHtml += '<div class="col-md-6">';
        statusHtml += `<div class="alert ${data.receiver_connected ? 'alert-success' : 'alert-danger'}">`;
        statusHtml += `<strong>Connection:</strong> ${data.receiver_connected ? '✅ Connected' : '❌ Disconnected'}`;
        statusHtml += '</div></div>';
        
        // Running Status
        statusHtml += '<div class="col-md-6">';
        statusHtml += `<div class="alert ${data.receiver_running ? 'alert-success' : 'alert-warning'}">`;
        statusHtml += `<strong>Receiver:</strong> ${data.receiver_running ? '🏃 Running' : '⏸️ Stopped'}`;
        statusHtml += '</div></div>';
        
        statusHtml += '</div>';
        
        // Configuration
        statusHtml += '<div class="mt-3">';
        statusHtml += '<h6>TG SMS Configuration:</h6>';
        statusHtml += '<ul>';
        statusHtml += `<li><strong>IP:</strong> ${data.tg_sms_config.ip}</li>`;
        statusHtml += `<li><strong>Port:</strong> ${data.tg_sms_config.port}</li>`;
        statusHtml += `<li><strong>Username:</strong> ${data.tg_sms_config.username}</li>`;
        statusHtml += `<li><strong>Total Received Messages:</strong> ${data.total_received_messages}</li>`;
        statusHtml += '</ul>';
        statusHtml += '</div>';
        
        statusDiv.innerHTML = statusHtml;
        
    } catch (error) {
        statusDiv.innerHTML = `<div class="alert alert-danger">Error checking status: ${error.message}</div>`;
    }
}

async function reconnectSMS() {
    const statusDiv = document.getElementById('smsStatus');
    statusDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Reconnecting to TG SMS server...';
    
    try {
        const response = await fetch('/debug/reconnect_sms');
        const data = await response.json();
        
        if (data.success) {
            statusDiv.innerHTML = `<div class="alert alert-success">✅ ${data.message}</div>`;
        } else {
            statusDiv.innerHTML = `<div class="alert alert-danger">❌ ${data.message}<br>Error: ${data.error || 'Unknown error'}</div>`;
        }
        
        // Refresh status after reconnection attempt
        setTimeout(checkSMSStatus, 2000);
        
    } catch (error) {
        statusDiv.innerHTML = `<div class="alert alert-danger">Error reconnecting: ${error.message}</div>`;
    }
}

async function checkConfig() {
    const configDiv = document.getElementById('configStatus');
    configDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Checking configuration...';
    
    try {
        const response = await fetch('/debug/config');
        const data = await response.json();
        
        let configHtml = '<div class="row">';
        
        // SMS API Config
        configHtml += '<div class="col-md-6">';
        configHtml += '<h6>SMS API Configuration:</h6>';
        configHtml += '<ul>';
        configHtml += `<li><strong>IP:</strong> ${data.SMS_API_CONFIG.ip}</li>`;
        configHtml += `<li><strong>Account:</strong> ${data.SMS_API_CONFIG.account}</li>`;
        configHtml += `<li><strong>Port:</strong> ${data.SMS_API_CONFIG.port}</li>`;
        configHtml += '</ul>';
        configHtml += '</div>';
        
        // TG SMS Config
        configHtml += '<div class="col-md-6">';
        configHtml += '<h6>TG SMS Configuration:</h6>';
        configHtml += '<ul>';
        configHtml += `<li><strong>IP:</strong> ${data.TG_SMS_CONFIG.ip}</li>`;
        configHtml += `<li><strong>Port:</strong> ${data.TG_SMS_CONFIG.port}</li>`;
        configHtml += `<li><strong>Username:</strong> ${data.TG_SMS_CONFIG.username}</li>`;
        configHtml += '</ul>';
        configHtml += '</div>';
        
        configHtml += '</div>';
        
        configDiv.innerHTML = configHtml;
        
    } catch (error) {
        configDiv.innerHTML = `<div class="alert alert-danger">Error checking config: ${error.message}</div>`;
    }
}

async function testSMSReceive() {
    const resultsDiv = document.getElementById('testResults');
    resultsDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Testing SMS receive processing...';
    
    try {
        const response = await fetch('/debug/test_sms_receive', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const data = await response.json();
        
        if (data.success) {
            resultsDiv.innerHTML = `
                <div class="alert alert-success">
                    ✅ ${data.message}
                    <details class="mt-2">
                        <summary>Sample Data Used:</summary>
                        <pre class="mt-2">${data.sample_data}</pre>
                    </details>
                </div>
            `;
        } else {
            resultsDiv.innerHTML = `<div class="alert alert-danger">❌ Test failed: ${data.error}</div>`;
        }
        
    } catch (error) {
        resultsDiv.innerHTML = `<div class="alert alert-danger">Error testing SMS receive: ${error.message}</div>`;
    }
}

// Auto-check status on page load
document.addEventListener('DOMContentLoaded', function() {
    checkSMSStatus();
});
</script>
{% endblock %}
