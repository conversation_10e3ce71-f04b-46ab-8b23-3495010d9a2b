{% extends "base.html" %}

{% block title %}Inbox - SMS Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-inbox"></i> Inbox
                {% if messages %}
                    <small class="text-muted">({{ messages.total }} messages)</small>
                {% endif %}
            </h1>
            <div>
                <button onclick="location.reload()" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-clockwise"></i> Refresh
                </button>
                <a href="{{ url_for('compose') }}" class="btn btn-primary">
                    <i class="bi bi-pencil-square"></i> Compose
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-search"></i>
                            </span>
                            <input type="text"
                                   class="form-control"
                                   name="search"
                                   placeholder="Search messages or phone numbers..."
                                   value="{{ request.args.get('search', '') }}">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" name="filter">
                            <option value="">All Messages</option>
                            <option value="today" {% if request.args.get('filter') == 'today' %}selected{% endif %}>Today</option>
                            <option value="week" {% if request.args.get('filter') == 'week' %}selected{% endif %}>This Week</option>
                            <option value="month" {% if request.args.get('filter') == 'month' %}selected{% endif %}>This Month</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-outline-secondary w-100">
                            <i class="bi bi-funnel"></i> Filter
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Messages List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list"></i> Received Messages
                </h5>
            </div>
            <div class="card-body p-0">
                {% if messages and messages.items %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th width="5%">
                                        <input type="checkbox" class="form-check-input" id="selectAll">
                                    </th>
                                    <th width="15%">From</th>
                                    <th width="45%">Message</th>
                                    <th width="10%">Port</th>
                                    <th width="15%">Received</th>
                                    <th width="10%">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for message in messages.items %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input message-checkbox" value="{{ message.id }}">
                                    </td>
                                    <td>
                                        <strong>{{ message.phone_number }}</strong>
                                    </td>
                                    <td>
                                        <div class="message-content">
                                            {{ message.content[:100] }}{% if message.content|length > 100 %}...{% endif %}
                                        </div>
                                        {% if message.content|length > 100 %}
                                            <small>
                                                <a href="#" class="text-primary" onclick="showFullMessage('{{ message.id }}')">
                                                    Show full message
                                                </a>
                                            </small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if message.gsm_port %}
                                            <span class="badge bg-secondary">Port {{ message.gsm_port }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ message.created_at.strftime('%Y-%m-%d') }}<br>
                                            {{ message.created_at.strftime('%H:%M:%S') }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('compose', to=message.phone_number, reply_to=message.message_id) }}"
                                               class="btn btn-outline-primary btn-sm"
                                               title="Reply">
                                                <i class="bi bi-reply"></i>
                                            </a>
                                            <a href="{{ url_for('conversation_view', phone_number=message.phone_number) }}"
                                               class="btn btn-outline-success btn-sm"
                                               title="View Conversation">
                                                <i class="bi bi-chat-dots"></i>
                                            </a>
                                            <button class="btn btn-outline-info btn-sm"
                                                    onclick="showMessageDetails('{{ message.id }}')"
                                                    title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm"
                                                    onclick="deleteMessage({{ message.id }}, '{{ message.phone_number }}')"
                                                    title="Delete">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if messages.pages > 1 %}
                    <div class="card-footer">
                        <nav aria-label="Message pagination">
                            <ul class="pagination justify-content-center mb-0">
                                {% if messages.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('inbox', page=messages.prev_num) }}">
                                            <i class="bi bi-chevron-left"></i> Previous
                                        </a>
                                    </li>
                                {% endif %}

                                {% for page_num in messages.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != messages.page %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('inbox', page=page_num) }}">{{ page_num }}</a>
                                            </li>
                                        {% else %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ page_num }}</span>
                                            </li>
                                        {% endif %}
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if messages.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('inbox', page=messages.next_num) }}">
                                            Next <i class="bi bi-chevron-right"></i>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>

                        <div class="text-center mt-2">
                            <small class="text-muted">
                                Showing {{ ((messages.page - 1) * messages.per_page) + 1 }} to
                                {{ messages.page * messages.per_page if messages.page * messages.per_page < messages.total else messages.total }}
                                of {{ messages.total }} messages
                            </small>
                        </div>
                    </div>
                    {% endif %}

                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-inbox text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">No messages in inbox</h4>
                        <p class="text-muted">You haven't received any SMS messages yet.</p>
                        <a href="{{ url_for('compose') }}" class="btn btn-primary">
                            <i class="bi bi-pencil-square"></i> Send your first message
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Bulk Actions (shown when messages are selected) -->
<div id="bulkActions" class="position-fixed bottom-0 start-50 translate-middle-x bg-primary text-white p-3 rounded-top shadow" style="display: none; z-index: 1000;">
    <div class="d-flex align-items-center gap-3">
        <span id="selectedCount">0</span> message(s) selected
        <button class="btn btn-light btn-sm" onclick="replyToSelected()">
            <i class="bi bi-reply-all"></i> Reply All
        </button>
        <button class="btn btn-outline-light btn-sm" onclick="clearSelection()">
            <i class="bi bi-x"></i> Clear
        </button>
    </div>
</div>

<!-- Message Details Modal -->
<div class="modal fade" id="messageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Message Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="messageModalBody">
                <!-- Content loaded via JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="replyButton">
                    <i class="bi bi-reply"></i> Reply
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Message selection handling
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const messageCheckboxes = document.querySelectorAll('.message-checkbox');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');

    function updateBulkActions() {
        const selected = document.querySelectorAll('.message-checkbox:checked');
        if (selected.length > 0) {
            selectedCount.textContent = selected.length;
            bulkActions.style.display = 'block';
        } else {
            bulkActions.style.display = 'none';
        }
    }

    selectAllCheckbox?.addEventListener('change', function() {
        messageCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });

    messageCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });

    window.clearSelection = function() {
        messageCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        selectAllCheckbox.checked = false;
        updateBulkActions();
    };

    window.replyToSelected = function() {
        const selected = document.querySelectorAll('.message-checkbox:checked');
        if (selected.length > 0) {
            // For now, just reply to the first selected message
            const firstMessageRow = selected[0].closest('tr');
            const phoneNumber = firstMessageRow.querySelector('td:nth-child(2) strong').textContent;
            window.location.href = `{{ url_for('compose') }}?to=${encodeURIComponent(phoneNumber)}`;
        }
    };
});

// Show full message
function showFullMessage(messageId) {
    // This would typically fetch the full message via AJAX
    alert('Full message view would be implemented here');
}

// Show message details
function showMessageDetails(messageId) {
    // This would typically fetch message details via AJAX
    const modal = new bootstrap.Modal(document.getElementById('messageModal'));
    document.getElementById('messageModalBody').innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `;
    modal.show();

    // Simulate loading message details
    setTimeout(() => {
        document.getElementById('messageModalBody').innerHTML = `
            <p><strong>Message ID:</strong> ${messageId}</p>
            <p><strong>Status:</strong> Received</p>
            <p>Message details would be loaded here via AJAX call to the backend.</p>
        `;
    }, 1000);
}

// Delete message
function deleteMessage(messageId, phoneNumber) {
    if (confirm(`Are you sure you want to delete this message from ${phoneNumber}? This action cannot be undone.`)) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/sms/${messageId}/delete`;

        // Add CSRF token if available
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}

// Auto-refresh every 30 seconds
setInterval(function() {
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 30000);
</script>
{% endblock %}
