{% extends "base.html" %}

{% block title %}User Management - SMS Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-people"></i> User Management
            </h1>
            <a href="{{ url_for('create_user') }}" class="btn btn-primary">
                <i class="bi bi-person-plus"></i> Add New User
            </a>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-table"></i> System Users
                </h5>
            </div>
            <div class="card-body">
                {% if users %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Role</th>
                                    <th>Permissions</th>
                                    <th>Assigned Ports</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-2">
                                                {{ user.username[0].upper() }}
                                            </div>
                                            <div>
                                                <strong>{{ user.username }}</strong>
                                                <br>
                                                <small class="text-muted">{{ user.email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'danger' if user.role == 'admin' else 'primary' if user.role == 'manager' else 'secondary' }}">
                                            {{ user.role.title() }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="permission-badges">
                                            {% if user.can_send_sms %}
                                                <span class="badge bg-success badge-sm">Send</span>
                                            {% endif %}
                                            {% if user.can_view_inbox %}
                                                <span class="badge bg-info badge-sm">Inbox</span>
                                            {% endif %}
                                            {% if user.can_view_outbox %}
                                                <span class="badge bg-info badge-sm">Outbox</span>
                                            {% endif %}
                                            {% if user.can_export %}
                                                <span class="badge bg-warning badge-sm">Export</span>
                                            {% endif %}
                                            {% if user.can_view_reports %}
                                                <span class="badge bg-primary badge-sm">Reports</span>
                                            {% endif %}
                                            {% if user.can_manage_users %}
                                                <span class="badge bg-danger badge-sm">Users</span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% set assigned_ports = user.get_assigned_ports() %}
                                        {% if assigned_ports %}
                                            {% for port in assigned_ports %}
                                                <span class="badge bg-secondary badge-sm">{{ port }}</span>
                                            {% endfor %}
                                        {% else %}
                                            <span class="text-muted">All ports</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-danger">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.last_login %}
                                            {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                                        {% else %}
                                            <span class="text-muted">Never</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" 
                                                    onclick="editUser({{ user.id }})"
                                                    title="Edit User">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            {% if user.username != 'admin' %}
                                                <button class="btn btn-outline-warning" 
                                                        onclick="toggleUserStatus({{ user.id }}, {{ user.is_active|lower }})"
                                                        title="{{ 'Deactivate' if user.is_active else 'Activate' }} User">
                                                    <i class="bi bi-{{ 'pause' if user.is_active else 'play' }}"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" 
                                                        onclick="deleteUser({{ user.id }}, '{{ user.username }}')"
                                                        title="Delete User">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">No Users Found</h4>
                        <p class="text-muted">Create your first user to get started.</p>
                        <a href="{{ url_for('create_user') }}" class="btn btn-primary">
                            <i class="bi bi-person-plus"></i> Add First User
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- User Statistics -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center bg-primary text-white">
            <div class="card-body">
                <h4>{{ users|selectattr("role", "equalto", "admin")|list|length }}</h4>
                <small>Administrators</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center bg-success text-white">
            <div class="card-body">
                <h4>{{ users|selectattr("role", "equalto", "manager")|list|length }}</h4>
                <small>Managers</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center bg-info text-white">
            <div class="card-body">
                <h4>{{ users|selectattr("role", "equalto", "user")|list|length }}</h4>
                <small>Regular Users</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center bg-warning text-white">
            <div class="card-body">
                <h4>{{ users|selectattr("is_active", "equalto", true)|list|length }}</h4>
                <small>Active Users</small>
            </div>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-gear"></i> Edit User
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Username</label>
                            <input type="text" class="form-control" id="editUsername" readonly>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Email</label>
                            <input type="email" class="form-control" id="editEmail">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Role</label>
                            <select class="form-select" id="editRole">
                                <option value="user">User</option>
                                <option value="manager">Manager</option>
                                <option value="admin">Administrator</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Status</label>
                            <select class="form-select" id="editStatus">
                                <option value="true">Active</option>
                                <option value="false">Inactive</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Permissions</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editCanSendSms">
                                    <label class="form-check-label" for="editCanSendSms">Send SMS</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editCanViewInbox">
                                    <label class="form-check-label" for="editCanViewInbox">View Inbox</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editCanViewOutbox">
                                    <label class="form-check-label" for="editCanViewOutbox">View Outbox</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editCanExport">
                                    <label class="form-check-label" for="editCanExport">Export Data</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editCanViewReports">
                                    <label class="form-check-label" for="editCanViewReports">View Reports</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editCanManageUsers">
                                    <label class="form-check-label" for="editCanManageUsers">Manage Users</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editCanManageSettings">
                                    <label class="form-check-label" for="editCanManageSettings">Manage Settings</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editCanManagePorts">
                                    <label class="form-check-label" for="editCanManagePorts">Manage Ports</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveUser()">Save Changes</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.permission-badges .badge {
    margin-right: 2px;
    margin-bottom: 2px;
}

.badge-sm {
    font-size: 0.7em;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function editUser(userId) {
    // This would fetch user data and populate the modal
    // For now, just show the modal
    const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
    modal.show();
}

function toggleUserStatus(userId, currentStatus) {
    if (confirm(`Are you sure you want to ${currentStatus ? 'deactivate' : 'activate'} this user?`)) {
        // Implement user status toggle
        console.log('Toggle user status:', userId, currentStatus);
        location.reload();
    }
}

function deleteUser(userId, username) {
    if (confirm(`Are you sure you want to delete user "${username}"? This action cannot be undone.`)) {
        // Implement user deletion
        console.log('Delete user:', userId, username);
        location.reload();
    }
}

function saveUser() {
    // Implement user saving
    console.log('Save user changes');
    const modal = bootstrap.Modal.getInstance(document.getElementById('editUserModal'));
    modal.hide();
    location.reload();
}
</script>
{% endblock %}
