{"name": "goautodial-sms", "version": "1.0.0", "description": "SMS Management System for MyGoautodial", "main": "index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && python app.py", "client": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "install-all": "npm install && cd frontend && npm install && pip install -r requirements.txt", "start": "cd backend && python app.py", "setup": "npm run install-all"}, "keywords": ["sms", "goautodial", "messaging", "communication"], "author": "MyGoautodial Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.0"}, "dependencies": {"axios": "^1.5.0", "cors": "^2.8.5", "express": "^4.18.2"}}