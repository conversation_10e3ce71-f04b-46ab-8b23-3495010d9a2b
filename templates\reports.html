{% extends "base.html" %}

{% block title %}Reports - SMS Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-graph-up"></i> SMS Reports & Analytics
        </h1>
    </div>
</div>

<!-- Summary Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center bg-primary text-white">
            <div class="card-body">
                <i class="bi bi-send" style="font-size: 2rem;"></i>
                <h3 class="mt-2">{{ total_sent }}</h3>
                <p class="mb-0">Total Sent</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center bg-success text-white">
            <div class="card-body">
                <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                <h3 class="mt-2">{{ total_received }}</h3>
                <p class="mb-0">Total Received</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center bg-info text-white">
            <div class="card-body">
                <i class="bi bi-chat-dots" style="font-size: 2rem;"></i>
                <h3 class="mt-2">{{ total_sent + total_received }}</h3>
                <p class="mb-0">Total Messages</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center bg-warning text-white">
            <div class="card-body">
                <i class="bi bi-calendar-range" style="font-size: 2rem;"></i>
                <h3 class="mt-2">30</h3>
                <p class="mb-0">Days Period</p>
            </div>
        </div>
    </div>
</div>

<!-- Daily Activity Chart -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-bar-chart"></i> Daily SMS Activity (Last 30 Days)
                </h5>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary active" onclick="showChart('daily')">Daily</button>
                    <button class="btn btn-outline-primary" onclick="showChart('weekly')">Weekly</button>
                </div>
            </div>
            <div class="card-body">
                <canvas id="dailyChart" width="400" height="100"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Top Contacts and Port Usage -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-people"></i> Top Contacts
                </h5>
            </div>
            <div class="card-body">
                {% if top_contacts %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Phone Number</th>
                                    <th class="text-end">Messages</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for contact in top_contacts %}
                                <tr>
                                    <td>
                                        <a href="{{ url_for('conversation_view', phone_number=contact[0]) }}" 
                                           class="text-decoration-none">
                                            {{ contact[0] }}
                                        </a>
                                    </td>
                                    <td class="text-end">
                                        <span class="badge bg-primary">{{ contact[1] }}</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">No contact data available</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-router"></i> Port Usage
                </h5>
            </div>
            <div class="card-body">
                {% if port_usage %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Port</th>
                                    <th class="text-end">Usage Count</th>
                                    <th class="text-end">Percentage</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% set total_usage = port_usage|sum(attribute=1) %}
                                {% for port in port_usage %}
                                <tr>
                                    <td>
                                        <i class="bi bi-hdd-network me-1"></i>
                                        Port {{ port[0] }}
                                    </td>
                                    <td class="text-end">{{ port[1] }}</td>
                                    <td class="text-end">
                                        {% set percentage = (port[1] / total_usage * 100) if total_usage > 0 else 0 %}
                                        {{ "%.1f"|format(percentage) }}%
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">No port usage data available</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Message Status Distribution -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-pie-chart"></i> Message Status Distribution
                </h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history"></i> Recent Activity
                </h5>
            </div>
            <div class="card-body">
                <div id="recentActivity">
                    <div class="text-center">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading recent activity...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Export Reports -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-download"></i> Export Reports
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <button class="btn btn-outline-primary w-100 mb-2" onclick="exportReport('daily')">
                            <i class="bi bi-calendar-day me-2"></i>
                            Daily Report
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-outline-success w-100 mb-2" onclick="exportReport('weekly')">
                            <i class="bi bi-calendar-week me-2"></i>
                            Weekly Report
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-outline-info w-100 mb-2" onclick="exportReport('monthly')">
                            <i class="bi bi-calendar-month me-2"></i>
                            Monthly Report
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Daily activity chart
const dailyData = {{ daily_stats|tojson }};
const ctx = document.getElementById('dailyChart').getContext('2d');

const dailyChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: dailyData.map(d => d.date),
        datasets: [{
            label: 'Sent',
            data: dailyData.map(d => d.sent),
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.1)',
            tension: 0.1
        }, {
            label: 'Received',
            data: dailyData.map(d => d.received),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Status distribution chart
const statusCtx = document.getElementById('statusChart').getContext('2d');

// Load status data
fetch('/api/sms/stats')
    .then(response => response.json())
    .then(data => {
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Sent', 'Received', 'Pending', 'Failed'],
                datasets: [{
                    data: [
                        data.total_sent || 0,
                        data.total_received || 0,
                        data.pending_messages || 0,
                        data.failed_messages || 0
                    ],
                    backgroundColor: [
                        '#36A2EB',
                        '#4BC0C0',
                        '#FFCE56',
                        '#FF6384'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    });

// Load recent activity
function loadRecentActivity() {
    fetch('/api/sms/inbox?per_page=5')
        .then(response => response.json())
        .then(data => {
            let html = '';
            if (data.messages && data.messages.length > 0) {
                data.messages.forEach(msg => {
                    const time = new Date(msg.created_at).toLocaleString();
                    const icon = msg.direction === 'inbound' ? 'inbox' : 'send';
                    const color = msg.direction === 'inbound' ? 'success' : 'primary';
                    
                    html += `
                        <div class="d-flex align-items-center mb-2">
                            <i class="bi bi-${icon} text-${color} me-2"></i>
                            <div class="flex-grow-1">
                                <small class="text-muted">${time}</small>
                                <div>${msg.direction === 'inbound' ? 'From' : 'To'}: ${msg.phone_number}</div>
                                <div class="text-muted small">${msg.content.substring(0, 50)}...</div>
                            </div>
                        </div>
                    `;
                });
            } else {
                html = '<p class="text-muted text-center">No recent activity</p>';
            }
            
            document.getElementById('recentActivity').innerHTML = html;
        })
        .catch(error => {
            document.getElementById('recentActivity').innerHTML = 
                '<p class="text-danger text-center">Error loading recent activity</p>';
        });
}

// Chart switching
function showChart(type) {
    // Update button states
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Update chart (you would implement different data loading here)
    console.log('Switching to', type, 'view');
}

// Export reports
function exportReport(type) {
    const today = new Date();
    let startDate, endDate;
    
    switch(type) {
        case 'daily':
            startDate = endDate = today.toISOString().split('T')[0];
            break;
        case 'weekly':
            const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
            startDate = weekStart.toISOString().split('T')[0];
            endDate = new Date().toISOString().split('T')[0];
            break;
        case 'monthly':
            const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
            startDate = monthStart.toISOString().split('T')[0];
            endDate = new Date().toISOString().split('T')[0];
            break;
    }
    
    const url = `/export/messages?start_date=${startDate}&end_date=${endDate}`;
    window.open(url, '_blank');
}

// Load recent activity on page load
document.addEventListener('DOMContentLoaded', function() {
    loadRecentActivity();
});
</script>
{% endblock %}
